// Debug script to analyze replay data and identify position mismatches
const replayData = {
  "word": "ACTINOMETERS",
  "positions": [[0,2],[2,4],[2,3],[1,2],[4,0],[2,0],[0,3],[4,3],[3,0],[0,1],[1,0],[1,4]],
  "boardBefore": [
    [{"letter":"F","col":0,"row":0},{"letter":"E","col":1,"row":0},{"letter":"A","col":2,"row":0},{"letter":"M","col":3,"row":0},{"letter":"N","col":4,"row":0}],
    [{"letter":"R","col":0,"row":1},{"letter":"M","col":1,"row":1},{"letter":"I","col":2,"row":1},{"letter":"W","col":3,"row":1},{"letter":"S","col":4,"row":1}],
    [{"letter":"O","col":0,"row":2},{"letter":"N","col":1,"row":2},{"letter":"D","col":2,"row":2},{"letter":"T","col":3,"row":2},{"letter":"C","col":4,"row":2}],
    [{"letter":"T","col":0,"row":3},{"letter":"P","col":1,"row":3},{"letter":"D","col":2,"row":3},{"letter":"G","col":3,"row":3},{"letter":"T","col":4,"row":3}],
    [{"letter":"N","col":0,"row":4},{"letter":"W","col":1,"row":4},{"letter":"C","col":2,"row":4},{"letter":"E","col":3,"row":4},{"letter":"R","col":4,"row":4}]
  ]
};

console.log("=== REPLAY DATA ANALYSIS ===");
console.log("Word:", replayData.word);
console.log("Expected letters:", replayData.word.split(''));
console.log("\n=== POSITION ANALYSIS ===");

const expectedLetters = replayData.word.split('');
const actualLetters = [];

replayData.positions.forEach((pos, index) => {
  const [row, col] = pos;
  const actualLetter = replayData.boardBefore[row][col].letter;
  const expectedLetter = expectedLetters[index];
  
  actualLetters.push(actualLetter);
  
  const match = actualLetter === expectedLetter ? "✓" : "✗";
  console.log(`Position ${index}: [${row},${col}] = "${actualLetter}" (expected "${expectedLetter}") ${match}`);
});

console.log("\n=== SUMMARY ===");
console.log("Expected word:", expectedLetters.join(''));
console.log("Actual letters:", actualLetters.join(''));
console.log("Match:", expectedLetters.join('') === actualLetters.join('') ? "✓ PERFECT MATCH" : "✗ MISMATCH");

// Check if there are any obvious issues
const boardLetters = replayData.boardBefore.flat().map(tile => tile.letter);
console.log("\n=== BOARD ANALYSIS ===");
console.log("All letters on board:", boardLetters.join(''));
console.log("Unique letters:", [...new Set(boardLetters)].sort().join(''));

// Check for duplicate positions
const positionStrings = replayData.positions.map(pos => `${pos[0]},${pos[1]}`);
const duplicatePositions = positionStrings.filter((pos, index) => positionStrings.indexOf(pos) !== index);
if (duplicatePositions.length > 0) {
  console.log("⚠️  DUPLICATE POSITIONS FOUND:", duplicatePositions);
} else {
  console.log("✓ No duplicate positions");
}
