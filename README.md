# LettersBot

An intelligent solver for the Letters game that uses advanced algorithms to find optimal word sequences and provides interactive game replay visualization.

## Architecture Overview

LettersBot consists of several key components:
- **Solver Engine**: Beam search algorithm with branch-and-bound optimization
- **Web Scraping**: Automated board state extraction from the Letters game website
- **Database Layer**: Cloudflare D1 for storing daily solver results
- **API Layer**: RESTful endpoints for triggering solves and retrieving results
- **Frontend**: Interactive game replay and analytics visualization

## Function Call Flow Diagram

The following diagram traces the complete function call flow from when a solver run is triggered through to when the game replay is displayed:

```mermaid
flowchart TD
    %% API Layer - Solver Run Trigger
    A["POST /api/run/+server.ts<br/>runSolverJob"] --> B["preloadDictionary<br/>$lib/bestWords.ts"]
    A --> C["chromium.launch<br/>@playwright/test"]
    C --> D["page.goto<br/>Letters game website"]
    D --> E["solveDailyBoard<br/>$lib/solver/solveDailyBoard.ts"]

    %% Game Logic - Solver Core
    E --> F["prepareGamePage<br/>$lib/solver/solveDailyBoard.ts"]
    E --> G["scrapeBoard<br/>$lib/solver/scraping.ts"]
    G --> H["GameState.fromBoard<br/>$lib/models/GameState.ts"]
    H --> I["beamSearch<br/>$lib/solver/solveDailyBoard.ts"]

    %% Search Algorithm
    I --> J["bestWords<br/>$lib/bestWords.ts"]
    J --> K["findWordPositions<br/>$lib/bestWords.ts"]
    K --> L["precisePlacementScore<br/>$lib/bestWords.ts"]
    I --> M["GameState.playMove<br/>$lib/models/GameState.ts"]
    M --> N["Board.apply<br/>$lib/models/Board.ts"]
    M --> O["Word.calculateScore<br/>$lib/models/Word.ts"]

    %% Enhanced Move Creation
    I --> P["createEnhancedMovesFromSolution<br/>$lib/solver/solveDailyBoard.ts"]
    P --> Q["wordToEnhancedMove<br/>$lib/types.ts"]

    %% Database Storage
    E --> R["insertBestLine<br/>$lib/db/bestLines.ts"]
    R --> S["withDatabaseRetry<br/>$lib/db/errorHandling.ts"]
    S --> T["D1 Database<br/>INSERT OR REPLACE"]

    %% API Layer - Data Retrieval
    U["GET /board/date/+server.ts<br/>Board API"] --> V["getBestLine<br/>$lib/db/bestLines.ts"]
    V --> W["withDatabaseRetry<br/>$lib/db/errorHandling.ts"]
    W --> X["D1 Database<br/>SELECT"]
    X --> Y["formatApiResponse<br/>$lib/routes/board/date/+server.ts"]

    %% Frontend Data Loading
    Z["/board/date/+page.ts<br/>Page Load"] --> AA["fetch /board/date"]
    AA --> U
    Y --> AB["BestLineResult<br/>with enhancedMoves"]
    AB --> AC["/board/date/+page.svelte<br/>Page Component"]

    %% UI Rendering
    AC --> AD["EnhancedBoardResults.svelte<br/>Main Results Component"]
    AD --> AE["GameReplayViewer.svelte<br/>Replay Component"]
    AE --> AF["displayBoard<br/>derived state function"]
    AF --> AG["selectedPositions<br/>derived state function"]
    AG --> AH["BoardVisualization.svelte<br/>Board Renderer"]
    AH --> AI["getTileClasses<br/>styling function"]
    AH --> AJ["getTileTooltip<br/>tooltip function"]
    AH --> AK["isSelected<br/>position check function"]

    %% Game State Management
    AE --> AL["goToMove<br/>navigation function"]
    AL --> AM["validateSelectedPositions<br/>validation function"]
    AM --> AN["isValidBoardState<br/>validation function"]

    %% Styling Groups
    classDef apiLayer fill:#e1f5fe,stroke:#01579b,stroke-width:2px
    classDef gameLogic fill:#f3e5f5,stroke:#4a148c,stroke-width:2px
    classDef database fill:#e8f5e8,stroke:#1b5e20,stroke-width:2px
    classDef frontend fill:#fff3e0,stroke:#e65100,stroke-width:2px
    classDef rendering fill:#fce4ec,stroke:#880e4f,stroke-width:2px

    %% Apply styles
    class A,U,Z,AA apiLayer
    class E,F,G,H,I,J,K,L,M,N,O,P,Q gameLogic
    class R,S,T,V,W,X database
    class AB,AC,AD,AE,AF,AG frontend
    class AH,AI,AJ,AK,AL,AM,AN rendering
```

### How to Read the Diagram

The diagram is organized into five color-coded functional groups:

- **🔵 API Layer (Light Blue)**: Entry points and HTTP request handling
- **🟣 Game Logic (Purple)**: Core solver algorithms and game state management
- **🟢 Database (Green)**: Data persistence and retrieval operations
- **🟠 Frontend (Orange)**: Client-side data loading and state management
- **🔴 Rendering (Pink)**: UI components and visualization functions

### Key Flow Paths

1. **Solver Trigger Flow**: `POST /api/run` → Browser automation → Game solving → Database storage
2. **Data Retrieval Flow**: Page load → API fetch → Database query → Data formatting
3. **Replay Rendering Flow**: Component mounting → State derivation → Board visualization → User interaction

### Critical Functions

- **`solveDailyBoard`**: Main solver orchestration function
- **`beamSearch`**: Core optimization algorithm using priority queue
- **`bestWords`**: Ultra-fast word finding with Hungarian algorithm optimization
- **`createEnhancedMovesFromSolution`**: Replay data generation with board state tracking
- **`GameReplayViewer`**: Interactive replay component with step-by-step navigation

## Board Analysis & Decision-Making Process

The following diagram illustrates the core solver logic and strategy for analyzing board states and making optimal move decisions:

```mermaid
flowchart TD
    %% Initial Board Analysis
    A["🎯 Initial Board State<br/>5x5 Grid with Letters & Multipliers"] --> B["📊 Board Analysis<br/>Extract tile frequencies<br/>Identify multiplier positions<br/>Calculate letter availability"]

    %% Beam Search Initialization
    B --> C["🚀 Initialize Beam Search<br/>Priority Queue (max-heap)<br/>State deduplication (BoardHashSet)<br/>Best solution tracking"]

    %% Main Search Loop
    C --> D["🔄 Beam Search Loop<br/>Pop highest-scoring node<br/>Check termination conditions"]

    %% Terminal State Check
    D --> E{"🏁 Terminal State?<br/>Turn ≥ 5 or<br/>No valid moves"}
    E -->|Yes| F["✅ Update Best Solution<br/>Store total score<br/>Record move sequence<br/>Create enhanced moves"]
    E -->|No| G["🌳 Generate Successors<br/>Find possible next moves"]

    %% Word Finding Process
    G --> H["🔍 bestWords Analysis<br/>Phase 1: Fast Filtering"]
    H --> I["📝 Dictionary Processing<br/>50,000+ word entries<br/>Letter frequency matching<br/>Greedy tile assignment"]
    I --> J["⚡ Rapid Scoring<br/>Approximate scores using<br/>best available multipliers<br/>Early termination bounds"]

    %% Precise Evaluation
    J --> K["🎯 Phase 2: Precise Evaluation<br/>Top candidates only"]
    K --> L["🧮 Hungarian Algorithm<br/>Optimal tile-to-position<br/>assignment for exact scoring"]
    L --> M["📈 Final Word Ranking<br/>Sort by actual scores<br/>Apply dynamic beam width"]

    %% Move Simulation
    M --> N["🎮 Move Simulation<br/>For each candidate word"]
    N --> O["🔄 GameState.playMove<br/>Calculate word score<br/>Apply to board state"]
    O --> P["⬇️ Board.apply<br/>Remove used tiles<br/>Drop remaining tiles<br/>Preserve multipliers"]

    %% State Evaluation
    P --> Q["📊 State Evaluation<br/>Calculate upper bound<br/>Estimate remaining potential"]
    Q --> R["✂️ Pruning Decision<br/>Branch-and-bound optimization"]

    %% Pruning Logic
    R --> S{"🌿 Should Prune?<br/>Upper bound + current ≤<br/>best known solution"}
    S -->|Yes| T["❌ Prune Branch<br/>Discard this path<br/>Increment pruned counter"]
    S -->|No| U["➕ Add to Queue<br/>Create successor node<br/>Update priority queue"]

    %% Queue Management
    T --> V["📋 Queue Status Check"]
    U --> V
    V --> W{"📦 Queue Empty?<br/>All nodes processed"}
    W -->|No| D
    W -->|Yes| X["🏆 Return Best Solution<br/>Optimal move sequence<br/>Maximum total score"]

    %% Solution Validation
    F --> Y["🔍 Solution Validation<br/>Verify move positions<br/>Check for mismatches"]
    Y --> Z{"✅ Valid Solution?<br/>All positions correct"}
    Z -->|Yes| AA["💾 Store Final Result<br/>Enhanced moves with<br/>board state tracking"]
    Z -->|No| BB["🔧 Correction Attempt<br/>Try to fix position<br/>mismatches"]
    BB --> CC{"🛠️ Correction Success?"}
    CC -->|Yes| AA
    CC -->|No| DD["⚠️ Use Original<br/>Preserve with warnings"]

    %% Final Output
    AA --> X
    DD --> X

    %% Styling
    classDef analysis fill:#e3f2fd,stroke:#1976d2,stroke-width:2px
    classDef search fill:#f3e5f5,stroke:#7b1fa2,stroke-width:2px
    classDef evaluation fill:#e8f5e8,stroke:#388e3c,stroke-width:2px
    classDef simulation fill:#fff3e0,stroke:#f57c00,stroke-width:2px
    classDef optimization fill:#fce4ec,stroke:#c2185b,stroke-width:2px
    classDef decision fill:#f1f8e9,stroke:#689f38,stroke-width:2px
    classDef output fill:#e0f2f1,stroke:#00796b,stroke-width:2px

    %% Apply styles
    class A,B analysis
    class C,D,G search
    class H,I,J,K,L,M evaluation
    class N,O,P simulation
    class Q,R,S,T,U,V,W optimization
    class E,Z,CC decision
    class F,X,Y,AA,BB,DD output
```

### Key Decision-Making Components

**🔍 Board Analysis Phase**
- Extracts letter frequencies and multiplier positions from the 5x5 grid
- Identifies optimal tile placement opportunities for maximum scoring
- Builds internal data structures for efficient word matching

**⚡ Two-Phase Word Finding**
- **Phase 1**: Fast greedy approximation using best available multipliers
- **Phase 2**: Precise Hungarian algorithm optimization for top candidates
- Dynamic beam width adjustment based on game turn (60/(turn+1) to 20 minimum)

**🧮 Scoring Strategy**
- Prioritizes tiles with higher letter and word multipliers
- Uses optimistic upper bound calculations for branch-and-bound pruning
- Maintains score bounds for early termination and performance optimization

**🌿 Pruning & Optimization**
- State deduplication using board hashing to avoid revisiting identical positions
- Branch-and-bound pruning when upper bound + current score ≤ best known solution
- Priority queue management with max-heap ordering by total + upper bound

**🎯 Move Selection Criteria**
- Evaluates up to 50 words per position with dynamic filtering
- Simulates each move's impact on board state and future potential
- Selects optimal sequence considering both immediate and long-term scoring opportunities
