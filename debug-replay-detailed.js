// Comprehensive debug script to analyze all moves and identify position mismatches
import fs from 'fs';
import http from 'http';

function fetchReplayData() {
	return new Promise((resolve, reject) => {
		const req = http.get('http://localhost:5174/board/2025-06-17', (res) => {
			let data = '';
			res.on('data', (chunk) => (data += chunk));
			res.on('end', () => {
				try {
					const parsed = JSON.parse(data);
					resolve(parsed.data);
				} catch (e) {
					reject(e);
				}
			});
		});
		req.on('error', reject);
	});
}

function analyzeMove(move, moveIndex) {
	console.log(`\n=== MOVE ${moveIndex + 1}: ${move.word} ===`);
	console.log(`Score: ${move.score}`);
	console.log(`Positions: ${JSON.stringify(move.positions)}`);

	if (!move.boardBefore) {
		console.log('❌ No boardBefore data');
		return { hasIssues: true, issues: ['Missing boardBefore'] };
	}

	const expectedLetters = move.word.split('');
	const actualLetters = [];
	const issues = [];

	console.log('\nPosition Analysis:');
	move.positions.forEach((pos, index) => {
		const [row, col] = pos;

		// Check bounds
		if (row < 0 || row >= 5 || col < 0 || col >= 5) {
			issues.push(`Position ${index} [${row},${col}] is out of bounds`);
			return;
		}

		// Check if board data exists
		if (!move.boardBefore[row] || !move.boardBefore[row][col]) {
			issues.push(`Position ${index} [${row},${col}] has no board data`);
			return;
		}

		const tile = move.boardBefore[row][col];
		const actualLetter = tile.letter;
		const expectedLetter = expectedLetters[index];

		actualLetters.push(actualLetter);

		const match = actualLetter === expectedLetter ? '✓' : '✗';
		console.log(
			`  ${index + 1}. [${row},${col}] = "${actualLetter}" (expected "${expectedLetter}") ${match}`
		);

		if (actualLetter !== expectedLetter) {
			issues.push(
				`Position ${index} [${row},${col}]: expected "${expectedLetter}", got "${actualLetter}"`
			);
		}

		// Validate tile structure
		if (typeof tile.base !== 'number' || tile.base < 1) {
			issues.push(`Position ${index} [${row},${col}]: invalid base score ${tile.base}`);
		}
		if (![1, 2, 3].includes(tile.letterMult)) {
			issues.push(`Position ${index} [${row},${col}]: invalid letterMult ${tile.letterMult}`);
		}
		if (![1, 2, 3].includes(tile.wordMult)) {
			issues.push(`Position ${index} [${row},${col}]: invalid wordMult ${tile.wordMult}`);
		}
		if (tile.row !== row || tile.col !== col) {
			issues.push(
				`Position ${index} [${row},${col}]: tile position mismatch (tile says [${tile.row},${tile.col}])`
			);
		}
	});

	const wordMatch = expectedLetters.join('') === actualLetters.join('');
	console.log(`\nWord Match: ${wordMatch ? '✓ PERFECT' : '✗ MISMATCH'}`);
	console.log(`Expected: ${expectedLetters.join('')}`);
	console.log(`Actual:   ${actualLetters.join('')}`);

	if (issues.length > 0) {
		console.log(`\n❌ Issues found:`);
		issues.forEach((issue) => console.log(`   - ${issue}`));
	} else {
		console.log(`\n✅ No issues found`);
	}

	return { hasIssues: issues.length > 0, issues, wordMatch };
}

async function main() {
	try {
		console.log('Fetching replay data from API...');
		const data = await fetchReplayData();

		if (!data.enhancedMoves) {
			console.log('❌ No enhanced moves data found');
			return;
		}

		console.log(`Found ${data.enhancedMoves.length} moves to analyze`);

		let totalIssues = 0;
		let movesWithIssues = 0;

		data.enhancedMoves.forEach((move, index) => {
			const analysis = analyzeMove(move, index);
			if (analysis.hasIssues) {
				movesWithIssues++;
				totalIssues += analysis.issues.length;
			}
		});

		console.log(`\n=== SUMMARY ===`);
		console.log(`Total moves: ${data.enhancedMoves.length}`);
		console.log(`Moves with issues: ${movesWithIssues}`);
		console.log(`Total issues: ${totalIssues}`);

		if (totalIssues === 0) {
			console.log('🎉 All moves have perfect position matches!');
			console.log('The issue might be in the UI rendering or data transformation.');
		} else {
			console.log('🔍 Position mismatches found in the data.');
		}
	} catch (error) {
		console.error('Error:', error.message);
	}
}

main();
