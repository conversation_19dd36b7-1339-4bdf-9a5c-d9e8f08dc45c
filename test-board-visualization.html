<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Board Visualization Test</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            margin: 20px;
            background: #f5f5f5;
        }
        .board {
            display: grid;
            grid-template-columns: repeat(5, 60px);
            grid-template-rows: repeat(5, 60px);
            gap: 2px;
            margin: 20px 0;
            background: #333;
            padding: 10px;
            border-radius: 8px;
        }
        .tile {
            background: white;
            border-radius: 4px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: bold;
            font-size: 18px;
            position: relative;
        }
        .tile.selected {
            background: #3b82f6;
            color: white;
        }
        .tile-info {
            position: absolute;
            top: 2px;
            right: 2px;
            font-size: 8px;
            color: #666;
        }
        .analysis {
            background: white;
            padding: 20px;
            border-radius: 8px;
            margin: 20px 0;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
        }
        .mismatch {
            color: red;
            font-weight: bold;
        }
        .match {
            color: green;
        }
    </style>
</head>
<body>
    <h1>Board Visualization Position Analysis</h1>
    
    <div class="analysis">
        <h2>Move: ACTINOMETERS</h2>
        <p><strong>Expected positions:</strong> <span id="expected-positions"></span></p>
        <p><strong>Expected letters:</strong> <span id="expected-letters"></span></p>
        <p><strong>Actual letters at positions:</strong> <span id="actual-letters"></span></p>
    </div>

    <div class="board" id="board"></div>

    <div class="analysis" id="position-analysis"></div>

    <script>
        // Replay data from the API
        const boardData = [
            [{"letter":"F","col":0,"row":0},{"letter":"E","col":1,"row":0},{"letter":"A","col":2,"row":0},{"letter":"M","col":3,"row":0},{"letter":"N","col":4,"row":0}],
            [{"letter":"R","col":0,"row":1},{"letter":"M","col":1,"row":1},{"letter":"I","col":2,"row":1},{"letter":"W","col":3,"row":1},{"letter":"S","col":4,"row":1}],
            [{"letter":"O","col":0,"row":2},{"letter":"N","col":1,"row":2},{"letter":"D","col":2,"row":2},{"letter":"T","col":3,"row":2},{"letter":"C","col":4,"row":2}],
            [{"letter":"T","col":0,"row":3},{"letter":"P","col":1,"row":3},{"letter":"D","col":2,"row":3},{"letter":"G","col":3,"row":3},{"letter":"T","col":4,"row":3}],
            [{"letter":"N","col":0,"row":4},{"letter":"W","col":1,"row":4},{"letter":"C","col":2,"row":4},{"letter":"E","col":3,"row":4},{"letter":"R","col":4,"row":4}]
        ];

        const selectedPositions = [[0,2],[2,4],[2,3],[1,2],[4,0],[2,0],[0,3],[4,3],[3,0],[0,1],[1,0],[1,4]];
        const expectedWord = "ACTINOMETERS";

        // Create board visualization
        const boardElement = document.getElementById('board');
        const selectedPositionSet = new Set(selectedPositions.map(pos => `${pos[0]},${pos[1]}`));

        for (let row = 0; row < 5; row++) {
            for (let col = 0; col < 5; col++) {
                const tile = document.createElement('div');
                tile.className = 'tile';
                
                const letter = boardData[row][col].letter;
                tile.textContent = letter;
                
                const posKey = `${row},${col}`;
                if (selectedPositionSet.has(posKey)) {
                    tile.classList.add('selected');
                }
                
                // Add position info
                const info = document.createElement('div');
                info.className = 'tile-info';
                info.textContent = `${row},${col}`;
                tile.appendChild(info);
                
                boardElement.appendChild(tile);
            }
        }

        // Analysis
        const expectedLetters = expectedWord.split('');
        const actualLetters = selectedPositions.map(pos => boardData[pos[0]][pos[1]].letter);
        
        document.getElementById('expected-positions').textContent = selectedPositions.map(pos => `[${pos[0]},${pos[1]}]`).join(', ');
        document.getElementById('expected-letters').textContent = expectedLetters.join('');
        document.getElementById('actual-letters').textContent = actualLetters.join('');

        // Detailed position analysis
        const analysisElement = document.getElementById('position-analysis');
        let analysisHTML = '<h3>Position-by-Position Analysis:</h3><ul>';
        
        selectedPositions.forEach((pos, index) => {
            const [row, col] = pos;
            const actualLetter = boardData[row][col].letter;
            const expectedLetter = expectedLetters[index];
            const match = actualLetter === expectedLetter;
            
            analysisHTML += `<li>Position ${index + 1}: [${row},${col}] = "${actualLetter}" (expected "${expectedLetter}") <span class="${match ? 'match' : 'mismatch'}">${match ? '✓' : '✗'}</span></li>`;
        });
        
        analysisHTML += '</ul>';
        
        const wordMatch = expectedLetters.join('') === actualLetters.join('');
        analysisHTML += `<p><strong>Overall Match:</strong> <span class="${wordMatch ? 'match' : 'mismatch'}">${wordMatch ? '✓ PERFECT MATCH' : '✗ MISMATCH'}</span></p>`;
        
        analysisElement.innerHTML = analysisHTML;
    </script>
</body>
</html>
