-- Migration: Enhanced moves support for LettersBot
-- Date: 2025-06-17
-- Description: Ensures the best_lines table can handle enhanced move data with tile positions

-- The existing best_lines table already stores JSON data, so no schema changes are needed.
-- This migration serves as documentation that the table now supports enhanced data structures.

-- Verify the table exists and has the expected structure
-- The 'data' column will now store BestLineResult objects that may include:
-- - enhancedMoves: Array of moves with exact tile positions and board states
-- - enhancedPerRound: Array of per-round moves with positions

-- Example of enhanced data structure that will be stored:
-- {
--   "total": 150,
--   "words": ["HELLO", "WORLD"],
--   "perRound": [
--     {"word": "HELLO", "score": 75},
--     {"word": "WORLD", "score": 75}
--   ],
--   "enhancedMoves": [
--     {
--       "word": "HELLO",
--       "score": 75,
--       "positions": [[0,0], [0,1], [0,2], [0,3], [0,4]],
--       "boardBefore": [...],
--       "boardAfter": [...]
--     }
--   ],
--   "enhancedPerRound": [...]
-- }

-- No actual SQL changes needed - this is a documentation migration
-- The existing JSON storage is flexible enough to handle the enhanced data structure

-- Verify table structure (this will succeed if the table exists)
SELECT name FROM sqlite_master WHERE type='table' AND name='best_lines';

-- Optional: Add a comment to document the enhanced data support
-- (SQLite doesn't support table comments, but this serves as documentation)
