import type { Request<PERSON><PERSON><PERSON> } from './$types';
import { insertBestLine, getBestLine } from '$lib/db';
import { solveDailyBoard } from '$lib/solver';
import { preloadDictionary } from '$lib/bestWords';
import type { BestLineResult } from '$lib/types';
import { chromium } from '@playwright/test';

/**
 * Webhook endpoint for triggering the daily solver
 *
 * This endpoint is designed to be called by external automation (e.g., cron jobs)
 * to run the solver once per day. It uses Cloudflare Browser Rendering in production
 * and <PERSON><PERSON> in development to solve the daily Letters game puzzle and stores
 * the result in D1.
 */

/**
 * Detect if we're running in development environment
 */
function isDevelopmentEnvironment(platform: any): boolean {
	// Check for development indicators
	const nodeEnv = platform?.env?.NODE_ENV;
	const hasCloudflareBinding = !!platform?.env?.BROWSER;

	// If NODE_ENV is explicitly set to development/preview, or if we don't have
	// Cloudflare Browser binding, assume development
	return nodeEnv === 'development' || nodeEnv === 'preview' || !hasCloudflareBinding;
}

/**
 * Verify webhook authentication
 */
function verifyAuthentication(request: Request, platform: any): boolean {
	const authHeader = request.headers.get('Authorization');
	const webhookSecret = platform?.env?.WEBHOOK_SECRET;

	// In production, require WEBHOOK_SECRET to be set
	if (!isDevelopmentEnvironment(platform)) {
		if (!webhookSecret) {
			throw new Error('WEBHOOK_SECRET environment variable is required in production');
		}
	}

	// Use fallback only in development
	const effectiveSecret =
		webhookSecret || (isDevelopmentEnvironment(platform) ? 'dev-secret-key' : '');

	if (!authHeader) {
		return false;
	}

	// Support both "Bearer token" and "token" formats
	const token = authHeader.startsWith('Bearer ') ? authHeader.slice(7) : authHeader;

	return token === effectiveSecret;
}

/**
 * Validate request body for the webhook
 */
function validateRequest(body: any): { date?: string; force?: boolean } {
	// Allow empty body for default behavior
	if (!body) {
		return {};
	}

	const result: { date?: string; force?: boolean } = {};

	// Validate date format if provided
	if (body.date) {
		if (typeof body.date !== 'string' || !/^\d{4}-\d{2}-\d{2}$/.test(body.date)) {
			throw new Error('Invalid date format. Expected YYYY-MM-DD');
		}
		result.date = body.date;
	}

	// Validate force flag if provided
	if (body.force !== undefined) {
		if (typeof body.force !== 'boolean') {
			throw new Error('Force flag must be a boolean');
		}
		result.force = body.force;
	}

	return result;
}

/**
 * Get today's date in YYYY-MM-DD format
 */
function getTodayDate(): string {
	return new Date().toISOString().split('T')[0];
}

/**
 * POST /api/run - Trigger the daily solver
 *
 * Request body (optional):
 * {
 *   "date": "2025-06-17",  // Optional: specific date to solve (defaults to today)
 *   "force": false         // Optional: force re-solve even if result exists
 * }
 *
 * Returns HTTP 202 and starts a Cloudflare Browser Rendering job
 */
export const POST: RequestHandler = async ({ request, platform, fetch }) => {
	try {
		// Verify authentication
		if (!verifyAuthentication(request, platform)) {
			return new Response(
				JSON.stringify({
					success: false,
					error: 'Unauthorized',
					message: 'Invalid or missing authentication token'
				}),
				{
					status: 401,
					headers: { 'Content-Type': 'application/json' }
				}
			);
		}

		// Check if we have access to required services
		if (!platform?.env?.DB) {
			return new Response(
				JSON.stringify({
					success: false,
					error: 'Service Unavailable',
					message: 'D1 database not available'
				}),
				{
					status: 503,
					headers: { 'Content-Type': 'application/json' }
				}
			);
		}

		// Check browser automation availability based on environment
		if (!isDevelopmentEnvironment(platform)) {
			// Production: require Cloudflare Browser Rendering
			if (!platform?.env?.BROWSER) {
				return new Response(
					JSON.stringify({
						success: false,
						error: 'Service Unavailable',
						message: 'Browser Rendering service not available'
					}),
					{
						status: 503,
						headers: { 'Content-Type': 'application/json' }
					}
				);
			}
		}
		// Development: Playwright is available via import, no additional check needed

		// Parse and validate request body
		let requestData: { date?: string; force?: boolean } = {};
		try {
			const body = await request.json().catch(() => ({}));
			requestData = validateRequest(body);
		} catch (validationError) {
			return new Response(
				JSON.stringify({
					success: false,
					error: 'Bad Request',
					message:
						validationError instanceof Error ? validationError.message : 'Invalid request body'
				}),
				{
					status: 400,
					headers: { 'Content-Type': 'application/json' }
				}
			);
		}

		const targetDate = requestData.date || getTodayDate();
		const force = requestData.force || false;

		// Check if we already have a result for this date (unless force is true)
		if (!force) {
			try {
				const existingResult = await getBestLine(platform.env.DB, targetDate);
				if (existingResult) {
					return new Response(
						JSON.stringify({
							success: true,
							message: 'Result already exists for this date',
							data: {
								date: targetDate,
								alreadyExists: true,
								result: existingResult
							}
						}),
						{
							status: 200,
							headers: { 'Content-Type': 'application/json' }
						}
					);
				}
			} catch (dbError) {
				console.warn(`Failed to check existing result for ${targetDate}:`, dbError);
				// Continue with solving - better to have duplicate than miss a day
			}
		}

		// Start the solver job asynchronously
		runSolverJob(platform, targetDate, fetch).catch((error) => {
			console.error(`[POST /api/run] Solver job failed for ${targetDate}:`, error);
		});

		// Return HTTP 202 immediately to indicate job started
		return new Response(
			JSON.stringify({
				success: true,
				message: 'Solver job started',
				data: {
					date: targetDate,
					jobStarted: true,
					timestamp: new Date().toISOString()
				}
			}),
			{
				status: 202,
				headers: { 'Content-Type': 'application/json' }
			}
		);
	} catch (error) {
		console.error('Webhook error:', error);

		return new Response(
			JSON.stringify({
				success: false,
				error: 'Internal Server Error',
				message: error instanceof Error ? error.message : 'Unknown error occurred',
				timestamp: new Date().toISOString()
			}),
			{
				status: 500,
				headers: { 'Content-Type': 'application/json' }
			}
		);
	}
};

/**
 * Run the solver job with browser automation
 * Uses Cloudflare Browser Rendering in production, Playwright in development
 */
async function runSolverJob(
	platform: any,
	date: string,
	fetch: (input: string | URL | globalThis.Request, init?: RequestInit) => Promise<Response>
): Promise<void> {
	try {
		console.log(`[runSolverJob] Starting solver for date: ${date}`);

		// Preload dictionary for edge runtime environment
		console.log(`[runSolverJob] Preloading dictionary...`);
		await preloadDictionary(fetch);
		console.log(`[runSolverJob] Dictionary preloaded successfully`);

		let browser: any;
		let page: any;
		let isPlaywright = false;

		if (isDevelopmentEnvironment(platform)) {
			// Development: Use Playwright
			console.log(`[runSolverJob] Using Playwright for development`);
			browser = await chromium.launch({
				headless: true // Run headless in development for automation
			});
			page = await browser.newPage();
			isPlaywright = true;
		} else {
			// Production: Use Cloudflare Browser Rendering
			console.log(`[runSolverJob] Using Cloudflare Browser Rendering for production`);
			const session = await platform.env.BROWSER.launch();
			page = await session.newPage();
			browser = session; // For cleanup
			isPlaywright = false;
		}

		try {
			// Navigate to the Letters game
			await page.goto('https://play.thelettersgame.com/');

			// Run the solver
			const result: BestLineResult = await solveDailyBoard(page);

			// Store the result in the database
			await insertBestLine(platform.env.DB, date, result);

			console.log(`[runSolverJob] Successfully solved and stored result for ${date}:`, {
				total: result.total,
				words: result.words,
				perRoundCount: result.perRound.length
			});
		} finally {
			// Always close the browser session
			if (isPlaywright) {
				await browser.close();
			} else {
				await browser.close(); // Cloudflare session close
			}
		}
	} catch (error) {
		console.error(`[runSolverJob] Failed to solve for date ${date}:`, error);
		// In a production system, you might want to store error information
		// or trigger alerts for failed solver runs
		throw error;
	}
}

/**
 * GET /api/run - Get status information about the solver service
 * This is mainly for health checks and debugging
 */
export const GET: RequestHandler = async ({ platform }) => {
	try {
		const isDev = isDevelopmentEnvironment(platform);
		const status = {
			service: 'LettersBot Solver Webhook',
			version: '1.0.0',
			timestamp: new Date().toISOString(),
			environment: isDev ? 'development' : 'production',
			services: {
				database: !!platform?.env?.DB,
				browser: isDev ? 'playwright' : platform?.env?.BROWSER ? 'cloudflare' : false,
				webhookSecret: isDev ? 'dev-fallback' : !!(platform?.env as any)?.WEBHOOK_SECRET
			}
		};

		return new Response(
			JSON.stringify({
				success: true,
				data: status
			}),
			{
				status: 200,
				headers: { 'Content-Type': 'application/json' }
			}
		);
	} catch (error) {
		return new Response(
			JSON.stringify({
				success: false,
				error: 'Internal Server Error',
				message: error instanceof Error ? error.message : 'Unknown error'
			}),
			{
				status: 500,
				headers: { 'Content-Type': 'application/json' }
			}
		);
	}
};
