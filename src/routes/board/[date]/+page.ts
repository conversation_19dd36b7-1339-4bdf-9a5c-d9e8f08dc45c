import type { PageLoad } from './$types';
import type { BestLineResult } from '$lib/types';

/**
 * Page load function for /board/[date]
 *
 * This function loads the board result data for the specified date
 * and provides it to the Svelte page component.
 */

export interface BoardPageData {
	date: string;
	result: BestLineResult | null;
	error?: string;
	available: boolean;
}

export const load: PageLoad = async ({ params, fetch }) => {
	const { date } = params;

	try {
		// Validate date format on client side
		if (!/^\d{4}-\d{2}-\d{2}$/.test(date)) {
			return {
				date,
				result: null,
				error: 'Invalid date format. Expected YYYY-MM-DD',
				available: false
			} satisfies BoardPageData;
		}

		// Fetch the board result from our API
		const response = await fetch(`/board/${date}`);
		const data = await response.json();

		if (!response.ok) {
			return {
				date,
				result: null,
				error: data.message || 'Failed to load board result',
				available: false
			} satisfies BoardPageData;
		}

		if (!data.success) {
			return {
				date,
				result: null,
				error: data.message || 'No result available',
				available: false
			} satisfies BoardPageData;
		}

		// Extract the result data
		const resultData = data.data;
		const result: BestLineResult = {
			total: resultData.total,
			words: resultData.words,
			perRound: resultData.perRound.map((round: any) => ({
				word: round.word,
				score: round.score
			})),
			// Include enhanced moves if available
			enhancedMoves: resultData.enhancedMoves,
			enhancedPerRound: resultData.enhancedPerRound
		};

		return {
			date,
			result,
			available: true
		} satisfies BoardPageData;
	} catch (error) {
		console.error('Failed to load board data:', error);

		return {
			date,
			result: null,
			error: 'Failed to load board result',
			available: false
		} satisfies BoardPageData;
	}
};
