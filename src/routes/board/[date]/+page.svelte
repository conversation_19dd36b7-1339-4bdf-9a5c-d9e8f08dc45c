<script lang="ts">
	import type { PageData } from './$types';
	import EnhancedBoardResults from '$lib/components/EnhancedBoardResults.svelte';

	let { data }: { data: PageData } = $props();

	// Format date for display
	function formatDate(dateStr: string): string {
		try {
			const date = new Date(dateStr + 'T00:00:00.000Z');
			return date.toLocaleDateString('en-US', {
				weekday: 'long',
				year: 'numeric',
				month: 'long',
				day: 'numeric'
			});
		} catch {
			return dateStr;
		}
	}
</script>

<svelte:head>
	<title>Letters Board - {data.date}</title>
	<meta name="description" content="LettersBot solver results for {formatDate(data.date)}" />
</svelte:head>

<div class="min-h-screen bg-gray-50 py-8">
	<div class="mx-auto max-w-6xl px-4 sm:px-6 lg:px-8">
		{#if data.error}
			<!-- Error State -->
			<div class="rounded-lg border border-red-200 bg-red-50 p-6 text-center">
				<div class="mb-2 text-red-600">
					<svg class="mx-auto h-12 w-12" fill="none" stroke="currentColor" viewBox="0 0 24 24">
						<path
							stroke-linecap="round"
							stroke-linejoin="round"
							stroke-width="2"
							d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.964-.833-2.732 0L3.732 16.5c-.77.833.192 2.5 1.732 2.5z"
						/>
					</svg>
				</div>
				<h2 class="mb-2 text-xl font-semibold text-red-800">No Results Available</h2>
				<p class="text-red-700">{data.error}</p>
				<div class="mt-4">
					<a
						href="/"
						class="inline-flex items-center rounded-md border border-transparent bg-red-600 px-4 py-2 text-sm font-medium text-white hover:bg-red-700 focus:ring-2 focus:ring-red-500 focus:ring-offset-2 focus:outline-none"
					>
						← Back to Home
					</a>
				</div>
			</div>
		{:else if data.result}
			<!-- Enhanced Results Display -->
			<EnhancedBoardResults result={data.result} date={data.date} />

			<!-- Navigation -->
			<div class="mt-8 flex justify-center space-x-4">
				<a
					href="/"
					class="inline-flex items-center rounded-md border border-gray-300 bg-white px-4 py-2 text-sm font-medium text-gray-700 hover:bg-gray-50 focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 focus:outline-none"
				>
					← Back to Home
				</a>
				<button
					onclick={() => {
						if (navigator.share) {
							navigator.share({ title: 'Letters Board Results', url: window.location.href });
						} else {
							navigator.clipboard.writeText(window.location.href);
						}
					}}
					class="inline-flex items-center rounded-md border border-transparent bg-blue-600 px-4 py-2 text-sm font-medium text-white hover:bg-blue-700 focus:ring-2 focus:ring-blue-500 focus:ring-offset-2 focus:outline-none"
				>
					Share Results
				</button>
			</div>
		{:else}
			<!-- Loading State -->
			<div class="rounded-lg bg-white p-6 text-center shadow-md">
				<div
					class="mx-auto mb-4 h-12 w-12 animate-spin rounded-full border-b-2 border-blue-600"
				></div>
				<p class="text-gray-600">Loading board results...</p>
			</div>
		{/if}
	</div>
</div>
