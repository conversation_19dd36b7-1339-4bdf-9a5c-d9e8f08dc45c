import type { <PERSON><PERSON><PERSON><PERSON><PERSON> } from './$types';
import { getBestLine } from '$lib/db';
import type { BestLineResult } from '$lib/types';

/**
 * API endpoint for retrieving board results by date
 *
 * This endpoint provides read-only access to stored solver results
 * for specific dates. It returns both JSON data and can be used
 * by the frontend UI components.
 */

/**
 * Validate date parameter
 */
function validateDateParam(date: string): boolean {
	// Check format: YYYY-MM-DD
	if (!/^\d{4}-\d{2}-\d{2}$/.test(date)) {
		return false;
	}

	// Check if it's a valid date
	const dateObj = new Date(date + 'T00:00:00.000Z');
	const isoString = dateObj.toISOString().split('T')[0];

	return isoString === date;
}

/**
 * Check if date is in the future
 */
function isFutureDate(date: string): boolean {
	const today = new Date().toISOString().split('T')[0];
	return date > today;
}

/**
 * Format result for API response
 */
function formatApiResponse(date: string, result: BestLineResult) {
	return {
		date,
		total: result.total,
		words: result.words,
		wordCount: result.words.length,
		perRound: result.perRound.map((round, index) => ({
			round: index + 1,
			word: round.word,
			score: round.score
		})),
		// Include enhanced moves if available
		enhancedMoves: result.enhancedMoves,
		enhancedPerRound: result.enhancedPerRound,
		metadata: {
			solvedAt: new Date().toISOString(), // This would be from the database in a real implementation
			version: '1.0.0'
		}
	};
}

/**
 * GET /board/[date] - Retrieve board result for a specific date
 *
 * Path parameter:
 *   date: Date in YYYY-MM-DD format
 *
 * Query parameters:
 *   format: 'json' | 'summary' (default: 'json')
 *
 * Returns the solver result for the specified date
 */
export const GET: RequestHandler = async ({ params, url, platform }) => {
	try {
		const { date } = params;

		// Validate date parameter
		if (!validateDateParam(date)) {
			return new Response(
				JSON.stringify({
					success: false,
					error: 'Bad Request',
					message: 'Invalid date format. Expected YYYY-MM-DD'
				}),
				{
					status: 400,
					headers: { 'Content-Type': 'application/json' }
				}
			);
		}

		// Check if date is in the future
		if (isFutureDate(date)) {
			return new Response(
				JSON.stringify({
					success: false,
					error: 'Bad Request',
					message: 'Cannot retrieve results for future dates'
				}),
				{
					status: 400,
					headers: { 'Content-Type': 'application/json' }
				}
			);
		}
		// Check database availability
		if (!platform?.env?.DB) {
			console.error('Database not available in platform environment');
			return new Response(
				JSON.stringify({
					success: false,
					error: 'Service Unavailable',
					message: 'Database service not available'
				}),
				{
					status: 503,
					headers: { 'Content-Type': 'application/json' }
				}
			);
		}

		// Get format from query parameters
		const format = url.searchParams.get('format') || 'json';
		if (!['json', 'summary'].includes(format)) {
			return new Response(
				JSON.stringify({
					success: false,
					error: 'Bad Request',
					message: 'Invalid format. Supported formats: json, summary'
				}),
				{
					status: 400,
					headers: { 'Content-Type': 'application/json' }
				}
			);
		}

		// Retrieve the result from database
		try {
			const result = await getBestLine(platform.env.DB, date);

			if (!result) {
				return new Response(
					JSON.stringify({
						success: false,
						error: 'Not Found',
						message: `No result found for date ${date}`,
						data: {
							date,
							available: false
						}
					}),
					{
						status: 404,
						headers: { 'Content-Type': 'application/json' }
					}
				);
			}

			// Format response based on requested format
			let responseData;
			if (format === 'summary') {
				responseData = {
					date,
					total: result.total,
					words: result.words,
					wordCount: result.words.length
				};
			} else {
				responseData = formatApiResponse(date, result);
			}

			return new Response(
				JSON.stringify({
					success: true,
					data: responseData
				}),
				{
					status: 200,
					headers: {
						'Content-Type': 'application/json',
						'Cache-Control': 'public, max-age=3600' // Cache for 1 hour
					}
				}
			);
		} catch (dbError) {
			console.error(`Database error retrieving result for ${date}:`, dbError);

			return new Response(
				JSON.stringify({
					success: false,
					error: 'Internal Server Error',
					message: 'Failed to retrieve result from database'
				}),
				{
					status: 500,
					headers: { 'Content-Type': 'application/json' }
				}
			);
		}
	} catch (error) {
		console.error('Board API error:', error);

		return new Response(
			JSON.stringify({
				success: false,
				error: 'Internal Server Error',
				message: error instanceof Error ? error.message : 'Unknown error occurred',
				timestamp: new Date().toISOString()
			}),
			{
				status: 500,
				headers: { 'Content-Type': 'application/json' }
			}
		);
	}
};

/**
 * OPTIONS /board/[date] - CORS preflight support
 */
export const OPTIONS: RequestHandler = async () => {
	return new Response(null, {
		status: 200,
		headers: {
			'Access-Control-Allow-Origin': '*',
			'Access-Control-Allow-Methods': 'GET, OPTIONS',
			'Access-Control-Allow-Headers': 'Content-Type',
			'Access-Control-Max-Age': '86400'
		}
	});
};
