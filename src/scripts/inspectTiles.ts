/**
 * <PERSON><PERSON><PERSON> to inspect individual tile structure on the Letters website
 */

import { chromium, type Browser, type Page } from '@playwright/test';

async function main() {
	console.log('🔍 Inspecting Letters Website Tile Structure');
	console.log('============================================\n');

	let browser: Browser | null = null;
	let page: Page | null = null;

	try {
		// Launch browser
		console.log('1. Launching browser...');
		browser = await chromium.launch({
			headless: false,
			slowMo: 500
		});

		page = await browser.newPage();
		page.setDefaultTimeout(30000);

		// Navigate to the Letters game
		console.log('2. Navigating to Letters game...');
		await page.goto('https://play.thelettersgame.com/', { waitUntil: 'networkidle' });
		await page.waitForTimeout(3000);

		// Look for "Play on Web" button
		console.log('3. Looking for "Play on Web" button...');
		const playButton = await page.$('button:has-text("Play on Web")');
		if (playButton) {
			console.log('   ✅ Found "Play on Web" button, clicking...');
			await playButton.click();
			await page.waitForTimeout(5000);
		}

		// Inspect the tile structure
		console.log('4. Inspecting tile structure...');
		const tileInfo = await page.evaluate(() => {
			const info: any = {
				boardFound: false,
				boardSelector: '',
				tiles: []
			};

			// Find the game board
			const boardSelectors = [
				'.grid.grid-cols-5',
				'.grid',
				'.game-board',
				'#game-board'
			];

			let boardElement: Element | null = null;
			for (const selector of boardSelectors) {
				boardElement = document.querySelector(selector);
				if (boardElement) {
					info.boardFound = true;
					info.boardSelector = selector;
					break;
				}
			}

			if (!boardElement) {
				return info;
			}

			// Get all child elements (tiles)
			const children = Array.from(boardElement.children);
			info.totalChildren = children.length;

			// Analyze first few tiles in detail
			for (let i = 0; i < Math.min(children.length, 10); i++) {
				const tile = children[i];
				const tileData: any = {
					index: i,
					tagName: tile.tagName,
					className: tile.className,
					id: tile.id,
					textContent: tile.textContent,
					innerHTML: tile.innerHTML,
					attributes: {},
					children: []
				};

				// Get all attributes
				for (let j = 0; j < tile.attributes.length; j++) {
					const attr = tile.attributes[j];
					tileData.attributes[attr.name] = attr.value;
				}

				// Get child elements
				for (let k = 0; k < tile.children.length; k++) {
					const child = tile.children[k];
					tileData.children.push({
						tagName: child.tagName,
						className: child.className,
						textContent: child.textContent,
						innerHTML: child.innerHTML
					});
				}

				info.tiles.push(tileData);
			}

			return info;
		});

		console.log('5. Tile Analysis Results:');
		console.log(`   Board found: ${tileInfo.boardFound}`);
		console.log(`   Board selector: ${tileInfo.boardSelector}`);
		console.log(`   Total children: ${tileInfo.totalChildren}`);

		if (tileInfo.tiles.length > 0) {
			console.log('\n   First 10 tiles:');
			for (const tile of tileInfo.tiles) {
				console.log(`\n   Tile ${tile.index}:`);
				console.log(`     Tag: ${tile.tagName}`);
				console.log(`     Class: ${tile.className}`);
				console.log(`     Text: "${tile.textContent}"`);
				console.log(`     Attributes:`, tile.attributes);
				if (tile.children.length > 0) {
					console.log(`     Children:`);
					for (const child of tile.children) {
						console.log(`       - ${child.tagName}.${child.className}: "${child.textContent}"`);
					}
				}
			}
		}

		// Test the current scraping selectors
		console.log('\n6. Testing current scraping selectors...');
		const selectorTests = await page.evaluate(() => {
			const results: any = {};
			
			const boardSelectors = [
				'.grid.grid-cols-5',
				'.grid',
				'.game-board',
				'#game-board',
				'.letters-grid',
				'.board-container',
				'[data-testid="game-board"]'
			];

			const tileSelectors = [
				'.grid.grid-cols-5 > div',
				'.grid > div',
				'.tile',
				'.letter-tile',
				'.game-tile',
				'.board-tile',
				'[data-testid="tile"]'
			];

			for (const selector of boardSelectors) {
				const elements = document.querySelectorAll(selector);
				results[`board_${selector}`] = elements.length;
			}

			for (const selector of tileSelectors) {
				const elements = document.querySelectorAll(selector);
				results[`tile_${selector}`] = elements.length;
			}

			return results;
		});

		console.log('   Selector test results:');
		for (const [selector, count] of Object.entries(selectorTests)) {
			console.log(`     ${selector}: ${count} elements`);
		}

		// Wait for user to inspect
		console.log('\n7. Waiting 10 seconds for manual inspection...');
		await page.waitForTimeout(10000);

	} catch (error) {
		console.error('❌ Error during inspection:', error);
	} finally {
		if (browser) {
			await browser.close();
		}
	}
}

main().catch(console.error);
