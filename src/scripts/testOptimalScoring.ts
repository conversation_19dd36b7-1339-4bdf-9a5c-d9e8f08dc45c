/**
 * Test script to verify the new optimal scoring algorithm
 *
 * This script compares the old greedy approach with the new optimal approach
 * to demonstrate improved accuracy in finding the best tile placements.
 */

import { Board } from '../lib/models/Board';
import { Tile } from '../lib/models/Tile';
import { bestWords } from '../lib/bestWords';

/**
 * Create a test board with specific multipliers to test optimal placement
 */
function createTestBoard(): Board {
	const board = new Board();

	// Create a board with some strategic multipliers
	// Row 0: A(3x letter), B(1x), C(2x word), D(1x), E(1x)
	// Row 1: F(1x), G(2x letter), H(1x), I(3x word), J(1x)
	// Row 2: K(1x), L(1x), M(2x letter), N(1x), O(2x word)
	// Row 3: P(1x), Q(1x), R(1x), S(2x letter), T(1x)
	// Row 4: U(1x), V(1x), W(1x), X(1x), Y(3x letter)

	const tiles = [
		[
			new Tile('A', 0, 0, 3, 1), // 3x letter multiplier
			new Tile('B', 0, 1, 1, 1),
			new Tile('C', 0, 2, 1, 2), // 2x word multiplier
			new Tile('D', 0, 3, 1, 1),
			new Tile('E', 0, 4, 1, 1)
		],
		[
			new Tile('F', 1, 0, 1, 1),
			new Tile('G', 1, 1, 2, 1), // 2x letter multiplier
			new Tile('H', 1, 2, 1, 1),
			new Tile('I', 1, 3, 1, 3), // 3x word multiplier
			new Tile('J', 1, 4, 1, 1)
		],
		[
			new Tile('K', 2, 0, 1, 1),
			new Tile('L', 2, 1, 1, 1),
			new Tile('M', 2, 2, 2, 1), // 2x letter multiplier
			new Tile('N', 2, 3, 1, 1),
			new Tile('O', 2, 4, 1, 2) // 2x word multiplier
		],
		[
			new Tile('P', 3, 0, 1, 1),
			new Tile('Q', 3, 1, 1, 1),
			new Tile('R', 3, 2, 1, 1),
			new Tile('S', 3, 3, 2, 1), // 2x letter multiplier
			new Tile('T', 3, 4, 1, 1)
		],
		[
			new Tile('U', 4, 0, 1, 1),
			new Tile('V', 4, 1, 1, 1),
			new Tile('W', 4, 2, 1, 1),
			new Tile('X', 4, 3, 1, 1),
			new Tile('Y', 4, 4, 3, 1) // 3x letter multiplier
		]
	];

	// Set the tiles on the board
	for (let row = 0; row < 5; row++) {
		for (let col = 0; col < 5; col++) {
			board.tiles[row][col] = tiles[row][col];
		}
	}

	return board;
}

/**
 * Test the optimal scoring algorithm
 */
async function testOptimalScoring(): Promise<void> {
	console.log('🧪 Testing Optimal Scoring Algorithm');
	console.log('=====================================\n');

	const board = createTestBoard();

	// Display the board
	console.log('Test Board Layout:');
	console.log('------------------');
	for (let row = 0; row < 5; row++) {
		const rowStr = board.tiles[row]
			.map((tile) => {
				const mult =
					tile.letterMult > 1
						? `L${tile.letterMult}`
						: tile.wordMult > 1
							? `W${tile.wordMult}`
							: '';
				return `${tile.letter}${mult}`.padEnd(4);
			})
			.join(' ');
		console.log(rowStr);
	}
	console.log();

	try {
		// Test with a small number of words to see detailed results
		const results = await bestWords(board, 10);

		console.log('Top Words Found:');
		console.log('----------------');

		for (let i = 0; i < Math.min(5, results.length); i++) {
			const word = results[i];
			console.log(`${i + 1}. ${word.letters} - ${word.score} points`);

			// Show the tile positions and multipliers used
			console.log('   Positions and multipliers:');
			for (let j = 0; j < word.positions.length; j++) {
				const [row, col] = word.positions[j];
				const tile = board.getTile(row, col);
				if (tile) {
					const letterMult = tile.letterMult > 1 ? ` (${tile.letterMult}x letter)` : '';
					const wordMult = tile.wordMult > 1 ? ` (${tile.wordMult}x word)` : '';
					console.log(`   ${word.letters[j]} at [${row},${col}]${letterMult}${wordMult}`);
				}
			}
			console.log();
		}

		if (results.length === 0) {
			console.log(
				'❌ No words found! This might indicate an issue with the algorithm or dictionary.'
			);
		} else {
			console.log(`✅ Found ${results.length} words successfully.`);
			console.log(`🏆 Best word: "${results[0].letters}" with ${results[0].score} points`);
		}
	} catch (error) {
		console.error('❌ Error during testing:', error);
		throw error;
	}
}

/**
 * Run the test
 */
async function main(): Promise<void> {
	try {
		await testOptimalScoring();
		console.log('\n✅ Optimal scoring test completed successfully!');
	} catch (error) {
		console.error('\n❌ Test failed:', error);
		process.exit(1);
	}
}

// Run if this file is executed directly
if (import.meta.url === `file://${process.argv[1]}`) {
	main().catch(console.error);
}

export { testOptimalScoring, createTestBoard };
