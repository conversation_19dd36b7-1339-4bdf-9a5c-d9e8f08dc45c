/**
 * Test script to verify the fixed scraping logic
 */

import { chromium, type Browser, type Page } from '@playwright/test';
import { scrapeBoard } from '../lib/solver/scraping';

async function main() {
	console.log('🔧 Testing Fixed Scraping Logic');
	console.log('===============================\n');

	let browser: Browser | null = null;
	let page: Page | null = null;

	try {
		// Launch browser
		console.log('1. Launching browser...');
		browser = await chromium.launch({
			headless: false,
			slowMo: 500
		});

		page = await browser.newPage();
		page.setDefaultTimeout(30000);

		// Navigate to the Letters game
		console.log('2. Navigating to Letters game...');
		await page.goto('https://play.thelettersgame.com/', { waitUntil: 'networkidle' });
		await page.waitForTimeout(3000);

		// Look for "Play on Web" button
		console.log('3. Looking for "Play on Web" button...');
		const playButton = await page.$('button:has-text("Play on Web")');
		if (playButton) {
			console.log('   ✅ Found "Play on Web" button, clicking...');
			await playButton.click();
			await page.waitForTimeout(5000);
		}

		// Test the scraping
		console.log('4. Testing scrapeBoard function...');
		try {
			const board = await scrapeBoard(page, {
				timeout: 10000,
				retryDelay: 1000,
				maxRetries: 3,
				debugScreenshots: true
			});

			console.log('   ✅ Board scraping successful!');
			console.log(`   📊 Board size: ${board.tiles.length}x${board.tiles[0]?.length}`);

			// Display the board
			console.log('\n   🎯 Scraped board state:');
			for (let row = 0; row < 5; row++) {
				const rowStr = board.tiles[row]
					.map(
						(tile) =>
							`${tile.letter}${tile.letterMult > 1 ? `(L${tile.letterMult})` : ''}${tile.wordMult > 1 ? `(W${tile.wordMult})` : ''}`
					)
					.join(' ');
				console.log(`      ${rowStr}`);
			}

			// Count multipliers
			let letterMults = 0;
			let wordMults = 0;
			for (let row = 0; row < 5; row++) {
				for (let col = 0; col < 5; col++) {
					const tile = board.tiles[row][col];
					if (tile.letterMult > 1) letterMults++;
					if (tile.wordMult > 1) wordMults++;
				}
			}

			console.log(`\n   📈 Multipliers found: ${letterMults} letter, ${wordMults} word`);

		} catch (error) {
			console.error('   ❌ Board scraping failed:', error);
			
			// Take a debug screenshot
			await page.screenshot({ 
				path: 'screenshots/scraping-debug.png', 
				fullPage: true 
			});
			console.log('   📸 Debug screenshot saved as scraping-debug.png');
		}

		// Wait for user to inspect
		console.log('\n5. Waiting 10 seconds for manual inspection...');
		await page.waitForTimeout(10000);

	} catch (error) {
		console.error('❌ Error during test:', error);
	} finally {
		if (browser) {
			await browser.close();
		}
	}
}

main().catch(console.error);
