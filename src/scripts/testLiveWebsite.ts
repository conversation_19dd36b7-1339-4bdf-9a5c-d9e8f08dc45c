/**
 * Test script for examining the live Letters game website
 *
 * This script opens the live website and examines its DOM structure
 * to understand how to interact with it and extract board data.
 */

import { chromium, type Browser, type Page } from '@playwright/test';
import { scrapeBoard, injectHelperFunctions } from '../lib/solver/scraping';
import { bestWords as bestWordsFast } from '../lib/bestWords';
import { playWord } from '../lib/solver/gameInteraction';

async function main() {
	console.log('🎯 Testing LettersBot on Live Website');
	console.log('=====================================\n');

	let browser: Browser | null = null;
	let page: Page | null = null;

	try {
		// Launch browser
		console.log('1. Launching browser...');
		browser = await chromium.launch({
			headless: false, // Keep visible for debugging
			slowMo: 500 // Slow down actions for visibility
		});

		page = await browser.newPage();

		// Set a longer timeout
		page.setDefaultTimeout(30000);

		// Navigate to the Letters game
		console.log('2. Navigating to Letters game...');
		await page.goto('https://play.thelettersgame.com/', { waitUntil: 'networkidle' });

		// Wait for page to load
		console.log('3. Waiting for page to fully load...');
		await page.waitForTimeout(5000); // Give extra time for any animations

		console.log('4. Taking landing page screenshot...');
		// Take a screenshot for reference
		await page.screenshot({ path: 'screenshots/live-website-landing.png', fullPage: true });
		console.log('   📸 Landing page screenshot saved as live-website-landing.png');

		console.log('5. Looking for "Play on Web" button...');
		// Look for the "Play on Web" button and click it
		const playButton = await page.$('button:has-text("Play on Web")');
		if (playButton) {
			console.log('   ✅ Found "Play on Web" button, clicking...');
			await playButton.click();

			// Wait for the game to load
			await page.waitForTimeout(5000);
			console.log('   ⏳ Waiting for game to load...');
		} else {
			console.log('   ⚠️  "Play on Web" button not found, proceeding anyway...');
		}

		console.log('6. Taking game screenshot...');
		await page.screenshot({ path: 'screenshots/live-website-game.png', fullPage: true });
		console.log('   📸 Game screenshot saved as live-website-game.png');

		console.log('7. Examining page structure...');
		// Examine the DOM structure
		await examinePageStructure(page);

		// Inject helper functions
		console.log('8. Injecting helper functions...');
		await injectHelperFunctions(page);

		// Test board scraping
		console.log('9. Testing board scraping...');
		await testBoardScraping(page);

		// Test word finding
		console.log('10. Testing word finding...');
		await testWordFinding(page);

		// Test word playing (if we successfully scraped the board)
		console.log('11. Testing word playing...');
		await testWordPlaying(page);
		console.log('\n✅ All tests completed successfully!');
	} catch (error) {
		console.error('❌ Error during testing:', error);
		console.error('Stack trace:', (error as Error).stack);

		if (page) {
			try {
				await page.screenshot({ path: 'screenshots/live-website-error.png', fullPage: true });
				console.log('   📸 Error screenshot saved as live-website-error.png');
			} catch (screenshotError) {
				console.error('Failed to take error screenshot:', screenshotError);
			}
		}
	} finally {
		if (browser) {
			console.log('\n12. Closing browser...');
			await browser.close();
		}
	}
}

async function examinePageStructure(page: Page): Promise<void> {
	console.log('   🔍 Examining DOM structure...');

	// Get page title
	const title = await page.title();
	console.log(`   📄 Page title: ${title}`);

	// Get all elements with class or id attributes
	console.log('   🔍 Finding all elements with classes or IDs...');
	const allElements = await page.evaluate(() => {
		const elements = Array.from(document.querySelectorAll('*[class], *[id]'));
		return elements.slice(0, 50).map((el) => ({
			tagName: el.tagName.toLowerCase(),
			className: el.className,
			id: el.id,
			textContent: el.textContent?.slice(0, 50) || ''
		}));
	});

	console.log('   📋 Found elements:');
	allElements.forEach((el, index) => {
		if (index < 20) {
			// Show first 20 elements
			console.log(
				`      ${el.tagName}${el.id ? `#${el.id}` : ''}${el.className ? `.${el.className.split(' ').join('.')}` : ''} - "${el.textContent}"`
			);
		}
	});

	// Look for common game board selectors
	const boardSelectors = [
		'.game-board',
		'#game-board',
		'.letters-grid',
		'.board-container',
		'[data-testid="game-board"]',
		'.board',
		'.grid',
		'.game-grid',
		'main',
		'.main',
		'#main',
		'.container',
		'.game',
		'.app'
	];

	console.log('   🎯 Searching for board container...');
	for (const selector of boardSelectors) {
		const element = await page.$(selector);
		if (element) {
			console.log(`   ✅ Found board container: ${selector}`);

			// Get some info about this element
			const boundingBox = await element.boundingBox();
			console.log(`      📐 Size: ${boundingBox?.width}x${boundingBox?.height}`);

			// Look for child elements
			const childCount = await element.evaluate((el) => el.children.length);
			console.log(`      👶 Child elements: ${childCount}`);

			// Get the HTML structure
			const innerHTML = await element.evaluate((el) => el.innerHTML.slice(0, 500));
			console.log(`      🏗️  Inner HTML (first 500 chars): ${innerHTML}`);

			break;
		}
	}

	// Look for tile elements
	const tileSelectors = [
		'.tile',
		'.letter-tile',
		'.game-tile',
		'.board-tile',
		'[data-testid="tile"]',
		'.cell',
		'.letter'
	];

	console.log('   🔤 Searching for tile elements...');
	for (const selector of tileSelectors) {
		const elements = await page.$$(selector);
		if (elements.length > 0) {
			console.log(`   ✅ Found ${elements.length} tiles with selector: ${selector}`);

			if (elements.length === 25) {
				console.log('      🎯 Perfect! Found exactly 25 tiles (5x5 board)');
			}

			// Examine first tile
			if (elements[0]) {
				const text = await elements[0].textContent();
				const classes = await elements[0].getAttribute('class');
				console.log(`      📝 First tile text: "${text}"`);
				console.log(`      🏷️  First tile classes: "${classes}"`);
			}

			break;
		}
	}

	// Look for game controls
	const controlSelectors = [
		'.submit-button',
		'#submit-word',
		'.play-button',
		'.confirm-button',
		'button[type="submit"]'
	];

	console.log('   🎮 Searching for game controls...');
	for (const selector of controlSelectors) {
		const element = await page.$(selector);
		if (element) {
			const text = await element.textContent();
			console.log(`   ✅ Found control: ${selector} - "${text}"`);
		}
	}
}

async function testBoardScraping(page: Page): Promise<void> {
	try {
		console.log('   🔍 Attempting to scrape board...');
		const board = await scrapeBoard(page);

		console.log('   ✅ Board scraping successful!');
		console.log(`   📊 Board size: ${board.tiles.length}x${board.tiles[0]?.length}`);

		// Display the board
		console.log('   🎯 Current board state:');
		for (let row = 0; row < 5; row++) {
			const rowStr = board.tiles[row]
				.map(
					(tile) =>
						`${tile.letter}${tile.letterMult > 1 ? `(L${tile.letterMult})` : ''}${tile.wordMult > 1 ? `(W${tile.wordMult})` : ''}`
				)
				.join(' ');
			console.log(`      ${rowStr}`);
		}

		return board;
	} catch (error) {
		console.error('   ❌ Board scraping failed:', error);
		throw error;
	}
}

async function testWordFinding(page: Page): Promise<void> {
	try {
		console.log('   🔍 Testing word finding...');
		const board = await scrapeBoard(page);

		console.log('   🧠 Finding best words...');
		const words = bestWordsFast(board, 5);

		console.log(`   ✅ Found ${words.length} possible words:`);
		words.forEach((word, index) => {
			console.log(`      ${index + 1}. ${word.letters} (score: ${word.score})`);
		});

		return words;
	} catch (error) {
		console.error('   ❌ Word finding failed:', error);
		throw error;
	}
}

async function testWordPlaying(page: Page): Promise<void> {
	try {
		console.log('   🎮 Testing word playing...');

		// Get the board and find a word
		const board = await scrapeBoard(page);
		const words = bestWordsFast(board, 1);

		if (words.length === 0) {
			console.log('   ⚠️  No words found to play');
			return;
		}

		const wordToPlay = words[0];
		console.log(`   🎯 Attempting to play word: ${wordToPlay.letters}`);

		// Take screenshot before playing
		await page.screenshot({ path: 'screenshots/before-word-play.png', fullPage: true });

		// Try to play the word
		await playWord(page, wordToPlay.positions);

		console.log('   ✅ Word playing completed!');

		// Take screenshot after playing
		await page.screenshot({ path: 'screenshots/after-word-play.png', fullPage: true });
	} catch (error) {
		console.error('   ❌ Word playing failed:', error);
		// Don't throw - this is expected to fail until we understand the interface
	}
}

// Run the test
console.log('🚀 Starting testLiveWebsite.ts script...');
main().catch((error) => {
	console.error('💥 Script failed with error:', error);
	process.exit(1);
});
