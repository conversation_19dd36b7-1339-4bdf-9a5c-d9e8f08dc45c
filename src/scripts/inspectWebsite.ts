/**
 * Simple script to inspect the Letters website DOM structure
 */

import { chromium, type Browser, type Page } from '@playwright/test';

async function main() {
	console.log('🔍 Inspecting Letters Website DOM Structure');
	console.log('==========================================\n');

	let browser: Browser | null = null;
	let page: Page | null = null;

	try {
		// Launch browser
		console.log('1. Launching browser...');
		browser = await chromium.launch({
			headless: false, // Keep visible for debugging
			slowMo: 500
		});

		page = await browser.newPage();
		page.setDefaultTimeout(30000);

		// Navigate to the Letters game
		console.log('2. Navigating to Letters game...');
		await page.goto('https://play.thelettersgame.com/', { waitUntil: 'networkidle' });
		await page.waitForTimeout(3000);

		// Take a screenshot
		console.log('3. Taking screenshot...');
		await page.screenshot({ path: 'screenshots/website-inspection.png', fullPage: true });

		// Look for "Play on Web" button
		console.log('4. Looking for "Play on Web" button...');
		const playButton = await page.$('button:has-text("Play on Web")');
		if (playButton) {
			console.log('   ✅ Found "Play on Web" button, clicking...');
			await playButton.click();
			await page.waitForTimeout(5000);

			// Take another screenshot after clicking
			await page.screenshot({ path: 'screenshots/after-play-button.png', fullPage: true });
		} else {
			console.log('   ⚠️  "Play on Web" button not found');
		}

		// Inspect the DOM structure
		console.log('5. Inspecting DOM structure...');
		const domInfo = await page.evaluate(() => {
			const info: any = {
				title: document.title,
				url: window.location.href,
				bodyClasses: document.body.className,
				allElements: []
			};

			// Look for potential game board elements
			const potentialSelectors = [
				'.grid',
				'.game-board',
				'#game-board',
				'.letters-grid',
				'.board-container',
				'[data-testid="game-board"]',
				'.board',
				'.game',
				'.tiles',
				'.tile',
				'[class*="grid"]',
				'[class*="board"]',
				'[class*="game"]',
				'[class*="tile"]'
			];

			for (const selector of potentialSelectors) {
				const elements = document.querySelectorAll(selector);
				if (elements.length > 0) {
					info.allElements.push({
						selector,
						count: elements.length,
						elements: Array.from(elements)
							.slice(0, 5)
							.map((el) => ({
								tagName: el.tagName,
								className: el.className,
								id: el.id,
								textContent: el.textContent?.slice(0, 50),
								children: el.children.length
							}))
					});
				}
			}

			// Get all elements with grid-related classes
			const gridElements = Array.from(document.querySelectorAll('*')).filter(
				(el) =>
					el.className &&
					typeof el.className === 'string' &&
					(el.className.includes('grid') ||
						el.className.includes('board') ||
						el.className.includes('game') ||
						el.className.includes('tile'))
			);

			info.gridElements = gridElements.slice(0, 10).map((el) => ({
				tagName: el.tagName,
				className: el.className,
				id: el.id,
				textContent: el.textContent?.slice(0, 50),
				children: el.children.length
			}));

			return info;
		});

		console.log('6. DOM Analysis Results:');
		console.log('   Title:', domInfo.title);
		console.log('   URL:', domInfo.url);
		console.log('   Body classes:', domInfo.bodyClasses);

		console.log('\n   Found elements by selector:');
		for (const item of domInfo.allElements) {
			console.log(`   ${item.selector}: ${item.count} elements`);
			for (const el of item.elements) {
				console.log(
					`     - ${el.tagName}.${el.className}${el.id ? '#' + el.id : ''} (${el.children} children)`
				);
			}
		}

		console.log('\n   Grid-related elements:');
		for (const el of domInfo.gridElements) {
			console.log(
				`   - ${el.tagName}.${el.className}${el.id ? '#' + el.id : ''} (${el.children} children)`
			);
			if (el.textContent) {
				console.log(`     Text: "${el.textContent}"`);
			}
		}

		// Wait for user to inspect
		console.log('\n7. Waiting 10 seconds for manual inspection...');
		await page.waitForTimeout(10000);
	} catch (error) {
		console.error('❌ Error during inspection:', error);
	} finally {
		if (browser) {
			await browser.close();
		}
	}
}

main().catch(console.error);
