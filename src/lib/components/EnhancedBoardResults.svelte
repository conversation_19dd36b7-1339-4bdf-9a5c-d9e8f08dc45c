<!--
  EnhancedBoardResults Component
  
  Main component that provides a comprehensive view of board results with:
  - Interactive game replay
  - Detailed move analytics
  - Enhanced board visualization
  - Tabbed interface for different views
-->

<script lang="ts">
	import GameReplayViewer from './GameReplayViewer.svelte';
	import MoveAnalytics from './MoveAnalytics.svelte';
	import BoardVisualization from './BoardVisualization.svelte';
	import type { BestLineResult } from '$lib/types';

	interface Props {
		/** The complete game result data */
		result: BestLineResult;
		/** Date string for the game */
		date: string;
	}

	let { result, date }: Props = $props();

	// Active tab state
	let activeTab = $state<'overview' | 'replay' | 'analytics' | 'comparison'>('overview');

	// Calculate summary statistics
	let summary = $derived(() => {
		const perRoundTotal = result.perRound.reduce((sum, round) => sum + round.score, 0);
		const improvement = result.total - perRoundTotal;
		const improvementPercentage = perRoundTotal > 0 ? (improvement / perRoundTotal) * 100 : 0;

		return {
			optimalTotal: result.total,
			greedyTotal: perRoundTotal,
			improvement,
			improvementPercentage,
			moveCount: result.words.length,
			hasEnhancedData: !!(result.enhancedMoves && result.enhancedMoves.length > 0)
		};
	});

	// Format date for display
	function formatDate(dateStr: string): string {
		try {
			const date = new Date(dateStr + 'T00:00:00.000Z');
			return date.toLocaleDateString('en-US', {
				weekday: 'long',
				year: 'numeric',
				month: 'long',
				day: 'numeric'
			});
		} catch {
			return dateStr;
		}
	}

	// Tab configuration
	let tabs = $derived([
		{ id: 'overview', label: 'Overview', icon: '📊' },
		{ id: 'replay', label: 'Game Replay', icon: '▶️', disabled: !summary().hasEnhancedData },
		{ id: 'analytics', label: 'Analytics', icon: '📈', disabled: !summary().hasEnhancedData },
		{ id: 'comparison', label: 'Comparison', icon: '⚖️' }
	] as const);

	// Handle tab change
	function setActiveTab(tabId: typeof activeTab) {
		activeTab = tabId;
	}
</script>

<div class="enhanced-board-results">
	<!-- Header -->
	<div class="mb-8 text-center">
		<h1 class="mb-2 text-3xl font-bold text-gray-900">Enhanced Board Results</h1>
		<p class="text-lg text-gray-600">{formatDate(date)}</p>

		{#if !summary().hasEnhancedData}
			<div class="mt-4 rounded-lg border border-yellow-200 bg-yellow-50 p-3">
				<p class="text-sm text-yellow-800">
					⚠️ Enhanced move data not available for this game. Some features may be limited.
				</p>
			</div>
		{/if}
	</div>

	<!-- Quick Stats -->
	<div class="mb-8 grid grid-cols-1 gap-4 md:grid-cols-4">
		<div class="rounded-lg bg-blue-50 p-4 text-center">
			<div class="text-3xl font-bold text-blue-600">{summary().optimalTotal}</div>
			<div class="text-sm text-gray-600">Optimal Score</div>
		</div>
		<div class="rounded-lg bg-green-50 p-4 text-center">
			<div class="text-3xl font-bold text-green-600">{summary().moveCount}</div>
			<div class="text-sm text-gray-600">Moves Played</div>
		</div>
		<div class="rounded-lg bg-purple-50 p-4 text-center">
			<div
				class="text-3xl font-bold {summary().improvement >= 0 ? 'text-green-600' : 'text-red-600'}"
			>
				{summary().improvement >= 0 ? '+' : ''}{summary().improvement}
			</div>
			<div class="text-sm text-gray-600">vs. Greedy</div>
		</div>
		<div class="rounded-lg bg-orange-50 p-4 text-center">
			<div
				class="text-3xl font-bold {summary().improvementPercentage >= 0
					? 'text-green-600'
					: 'text-red-600'}"
			>
				{summary().improvementPercentage >= 0 ? '+' : ''}{summary().improvementPercentage.toFixed(
					1
				)}%
			</div>
			<div class="text-sm text-gray-600">Efficiency</div>
		</div>
	</div>

	<!-- Tab Navigation -->
	<div class="mb-6">
		<nav class="flex space-x-1 rounded-lg bg-gray-100 p-1">
			{#each tabs as tab}
				<button
					onclick={() => setActiveTab(tab.id)}
					disabled={'disabled' in tab ? tab.disabled : false}
					class="flex flex-1 items-center justify-center space-x-2 rounded-md px-4 py-2 text-sm font-medium transition-colors
						{activeTab === tab.id
						? 'bg-white text-blue-600 shadow-sm'
						: 'disabled' in tab && tab.disabled
							? 'cursor-not-allowed text-gray-400'
							: 'text-gray-600 hover:bg-gray-50 hover:text-gray-900'}"
				>
					<span>{tab.icon}</span>
					<span>{tab.label}</span>
				</button>
			{/each}
		</nav>
	</div>

	<!-- Tab Content -->
	<div class="tab-content">
		{#if activeTab === 'overview'}
			<!-- Overview Tab -->
			<div class="space-y-6">
				<!-- Optimal Solution Display -->
				<div class="rounded-lg bg-white p-6 shadow-lg">
					<h2 class="mb-4 text-xl font-semibold text-gray-900">Optimal Solution</h2>
					<div class="grid grid-cols-1 gap-3 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-5">
						{#each result.words as word, index}
							<div class="rounded-lg border border-blue-200 bg-blue-50 p-4 text-center">
								<div class="text-sm font-medium text-blue-600">Move {index + 1}</div>
								<div class="font-mono text-xl font-bold text-blue-900">{word.toUpperCase()}</div>
								{#if result.enhancedMoves && result.enhancedMoves[index]}
									<div class="mt-1 text-sm text-gray-600">
										{result.enhancedMoves[index].score} points
									</div>
								{/if}
							</div>
						{/each}
					</div>
				</div>

				<!-- Quick Comparison -->
				<div class="rounded-lg bg-white p-6 shadow-lg">
					<h2 class="mb-4 text-xl font-semibold text-gray-900">Performance Summary</h2>
					<div class="grid grid-cols-1 gap-6 md:grid-cols-2">
						<div>
							<h3 class="mb-2 font-medium text-gray-900">Score Breakdown</h3>
							<div class="space-y-2 text-sm">
								<div class="flex justify-between">
									<span class="text-gray-600">Optimal Strategy:</span>
									<span class="font-medium text-blue-600">{summary().optimalTotal} points</span>
								</div>
								<div class="flex justify-between">
									<span class="text-gray-600">Greedy Strategy:</span>
									<span class="font-medium text-gray-600">{summary().greedyTotal} points</span>
								</div>
								<div class="flex justify-between border-t pt-2">
									<span class="font-medium text-gray-900">Improvement:</span>
									<span
										class="font-bold {summary().improvement >= 0
											? 'text-green-600'
											: 'text-red-600'}"
									>
										{summary().improvement >= 0 ? '+' : ''}{summary().improvement} points
									</span>
								</div>
							</div>
						</div>
						<div>
							<h3 class="mb-2 font-medium text-gray-900">Strategy Efficiency</h3>
							<div class="space-y-2 text-sm">
								<div class="flex justify-between">
									<span class="text-gray-600">Average per Move:</span>
									<span class="font-medium"
										>{(summary().optimalTotal / summary().moveCount).toFixed(1)} points</span
									>
								</div>
								<div class="flex justify-between">
									<span class="text-gray-600">Efficiency Gain:</span>
									<span
										class="font-medium {summary().improvementPercentage >= 0
											? 'text-green-600'
											: 'text-red-600'}"
									>
										{summary().improvementPercentage >= 0
											? '+'
											: ''}{summary().improvementPercentage.toFixed(1)}%
									</span>
								</div>
								<div class="flex justify-between border-t pt-2">
									<span class="font-medium text-gray-900">Total Moves:</span>
									<span class="font-bold">{summary().moveCount}</span>
								</div>
							</div>
						</div>
					</div>
				</div>
			</div>
		{:else if activeTab === 'replay' && result.enhancedMoves}
			<!-- Game Replay Tab -->
			<GameReplayViewer moves={result.enhancedMoves} autoPlay={false} autoPlaySpeed={2500} />
		{:else if activeTab === 'analytics' && result.enhancedMoves}
			<!-- Analytics Tab -->
			<MoveAnalytics moves={result.enhancedMoves} greedyMoves={result.perRound} />
		{:else if activeTab === 'comparison'}
			<!-- Comparison Tab -->
			<div class="rounded-lg bg-white p-6 shadow-lg">
				<h2 class="mb-4 text-xl font-semibold text-gray-900">Strategy Comparison</h2>
				<div class="overflow-x-auto">
					<table class="min-w-full divide-y divide-gray-200">
						<thead class="bg-gray-50">
							<tr>
								<th
									class="px-6 py-3 text-left text-xs font-medium tracking-wider text-gray-500 uppercase"
									>Round</th
								>
								<th
									class="px-6 py-3 text-left text-xs font-medium tracking-wider text-gray-500 uppercase"
									>Greedy Word</th
								>
								<th
									class="px-6 py-3 text-left text-xs font-medium tracking-wider text-gray-500 uppercase"
									>Greedy Score</th
								>
								<th
									class="px-6 py-3 text-left text-xs font-medium tracking-wider text-gray-500 uppercase"
									>Optimal Word</th
								>
								<th
									class="px-6 py-3 text-left text-xs font-medium tracking-wider text-gray-500 uppercase"
									>Optimal Score</th
								>
								<th
									class="px-6 py-3 text-left text-xs font-medium tracking-wider text-gray-500 uppercase"
									>Difference</th
								>
							</tr>
						</thead>
						<tbody class="divide-y divide-gray-200 bg-white">
							{#each result.perRound as round, index}
								{@const optimalScore = result.enhancedMoves?.[index]?.score || 0}
								{@const difference = optimalScore - round.score}
								<tr class="hover:bg-gray-50">
									<td class="px-6 py-4 text-sm font-medium whitespace-nowrap text-gray-900"
										>{index + 1}</td
									>
									<td class="px-6 py-4 font-mono text-sm whitespace-nowrap text-gray-900"
										>{round.word.toUpperCase()}</td
									>
									<td class="px-6 py-4 text-sm whitespace-nowrap text-gray-900">{round.score}</td>
									<td class="px-6 py-4 font-mono text-sm whitespace-nowrap text-gray-900"
										>{result.words[index]?.toUpperCase() || '-'}</td
									>
									<td class="px-6 py-4 text-sm whitespace-nowrap text-gray-900"
										>{optimalScore || '-'}</td
									>
									<td
										class="px-6 py-4 text-sm whitespace-nowrap {difference > 0
											? 'text-green-600'
											: difference < 0
												? 'text-red-600'
												: 'text-gray-900'}"
									>
										{difference > 0 ? '+' : ''}{difference || '-'}
									</td>
								</tr>
							{/each}
						</tbody>
					</table>
				</div>
			</div>
		{/if}
	</div>
</div>
