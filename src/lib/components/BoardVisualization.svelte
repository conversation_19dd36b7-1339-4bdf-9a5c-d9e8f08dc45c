<!--
  BoardVisualization Component
  
  Displays a 5x5 Letters game board with enhanced visualization features:
  - Highlighted selected tiles
  - Multiplier indicators
  - Hover effects and tooltips
  - Animation support for tile changes
-->

<script lang="ts">
	import type { Tile } from '$lib/types';

	interface Props {
		/** 5x5 grid of tiles to display */
		board: Tile[][];
		/** Positions of selected tiles to highlight */
		selectedPositions?: Array<[number, number]>;
		/** Whether to show multiplier indicators */
		showMultipliers?: boolean;
		/** Size of each tile in pixels */
		tileSize?: number;
		/** Whether to animate tile changes */
		animated?: boolean;
		/** Custom CSS class for the board container */
		class?: string;
		/** Callback when a tile is clicked */
		onTileClick?: (row: number, col: number, tile: Tile) => void;
	}

	let {
		board,
		selectedPositions = [],
		showMultipliers = true,
		tileSize = 48,
		animated = false,
		class: className = '',
		onTileClick
	}: Props = $props();

	// Create a set of selected positions for fast lookup
	let selectedSet = $derived(
		new Set(selectedPositions.map(([r, c]) => `${r},${c}`))
	);

	// Check if a position is selected
	function isSelected(row: number, col: number): boolean {
		return selectedSet.has(`${row},${col}`);
	}

	// Get tile classes based on state
	function getTileClasses(tile: Tile, row: number, col: number): string {
		const baseClasses = [
			'flex items-center justify-center border border-gray-300 font-mono text-sm font-medium',
			'transition-all duration-200 cursor-pointer hover:shadow-md'
		];

		// Selection state
		if (isSelected(row, col)) {
			baseClasses.push('bg-blue-200 border-blue-500 text-blue-900 font-bold shadow-md');
		} else {
			baseClasses.push('bg-white hover:bg-gray-50');
		}

		// Multiplier styling
		if (showMultipliers) {
			if (tile.letterMult > 1) {
				baseClasses.push('ring-2 ring-orange-300');
			}
			if (tile.wordMult > 1) {
				baseClasses.push('ring-2 ring-purple-300');
			}
			if (tile.letterMult > 1 && tile.wordMult > 1) {
				baseClasses.push('ring-2 ring-red-300');
			}
		}

		// Animation
		if (animated) {
			baseClasses.push('transform hover:scale-105');
		}

		return baseClasses.join(' ');
	}

	// Get tooltip text for a tile
	function getTileTooltip(tile: Tile, row: number, col: number): string {
		const parts = [`${tile.letter} at [${row},${col}]`];
		
		if (tile.base > 1) {
			parts.push(`Base: ${tile.base} pts`);
		}
		
		if (tile.letterMult > 1) {
			parts.push(`Letter: ${tile.letterMult}x`);
		}
		
		if (tile.wordMult > 1) {
			parts.push(`Word: ${tile.wordMult}x`);
		}

		return parts.join(' | ');
	}

	// Handle tile click
	function handleTileClick(row: number, col: number, tile: Tile) {
		if (onTileClick) {
			onTileClick(row, col, tile);
		}
	}
</script>

<div class="board-container {className}" style="--tile-size: {tileSize}px">
	<div class="grid grid-cols-5 gap-1 w-fit mx-auto">
		{#each board as row, rowIndex (rowIndex)}
			{#each row as tile, colIndex (`${rowIndex}-${colIndex}`)}
				<div
					class={getTileClasses(tile, rowIndex, colIndex)}
					style="width: var(--tile-size); height: var(--tile-size);"
					title={getTileTooltip(tile, rowIndex, colIndex)}
					onclick={() => handleTileClick(rowIndex, colIndex, tile)}
					role="button"
					tabindex="0"
					onkeydown={(e) => {
						if (e.key === 'Enter' || e.key === ' ') {
							e.preventDefault();
							handleTileClick(rowIndex, colIndex, tile);
						}
					}}
				>
					<!-- Letter -->
					<span class="relative z-10">
						{tile.letter}
					</span>

					<!-- Multiplier indicators -->
					{#if showMultipliers && (tile.letterMult > 1 || tile.wordMult > 1)}
						<div class="absolute inset-0 flex items-end justify-end p-0.5">
							<div class="flex flex-col text-xs leading-none">
								{#if tile.letterMult > 1}
									<span class="text-orange-600 font-bold">L{tile.letterMult}</span>
								{/if}
								{#if tile.wordMult > 1}
									<span class="text-purple-600 font-bold">W{tile.wordMult}</span>
								{/if}
							</div>
						</div>
					{/if}

					<!-- Selection indicator -->
					{#if isSelected(rowIndex, colIndex)}
						<div class="absolute inset-0 flex items-start justify-start p-0.5">
							<div class="w-2 h-2 bg-blue-600 rounded-full"></div>
						</div>
					{/if}
				</div>
			{/each}
		{/each}
	</div>

	<!-- Legend -->
	{#if showMultipliers}
		<div class="mt-4 flex justify-center">
			<div class="flex space-x-4 text-xs text-gray-600">
				<div class="flex items-center space-x-1">
					<div class="w-3 h-3 border-2 border-orange-300 rounded"></div>
					<span>Letter Multiplier</span>
				</div>
				<div class="flex items-center space-x-1">
					<div class="w-3 h-3 border-2 border-purple-300 rounded"></div>
					<span>Word Multiplier</span>
				</div>
				<div class="flex items-center space-x-1">
					<div class="w-3 h-3 bg-blue-200 border border-blue-500 rounded"></div>
					<span>Selected</span>
				</div>
			</div>
		</div>
	{/if}
</div>

<style>
	.board-container {
		position: relative;
	}

	/* Ensure proper positioning for absolute elements */
	.grid > div {
		position: relative;
	}
</style>
