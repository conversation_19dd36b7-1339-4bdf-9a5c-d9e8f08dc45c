/**
 * Tests for GameReplayViewer component
 *
 * This test suite verifies that the replay functionality correctly displays
 * the original letters that were clicked during each move, not the letters
 * that appear at those positions after the move was played.
 */

import { describe, it, expect, beforeEach } from 'vitest';
import type { EnhancedMove, Tile } from '$lib/types';

describe('GameReplayViewer Display Logic', () => {
	let mockMoves: EnhancedMove[];
	let mockInitialBoard: Tile[][];

	beforeEach(() => {
		// Create a mock initial board state
		mockInitialBoard = [
			[
				{ letter: 'H', base: 4, letterMult: 1, wordMult: 1, row: 0, col: 0 },
				{ letter: 'E', base: 1, letterMult: 1, wordMult: 1, row: 0, col: 1 },
				{ letter: 'L', base: 1, letterMult: 1, wordMult: 1, row: 0, col: 2 },
				{ letter: 'L', base: 1, letterMult: 1, wordMult: 1, row: 0, col: 3 },
				{ letter: 'O', base: 1, letterMult: 1, wordMult: 1, row: 0, col: 4 }
			],
			[
				{ letter: 'W', base: 4, letterMult: 1, wordMult: 1, row: 1, col: 0 },
				{ letter: 'O', base: 1, letterMult: 1, wordMult: 1, row: 1, col: 1 },
				{ letter: 'R', base: 1, letterMult: 1, wordMult: 1, row: 1, col: 2 },
				{ letter: 'L', base: 1, letterMult: 1, wordMult: 1, row: 1, col: 3 },
				{ letter: 'D', base: 2, letterMult: 1, wordMult: 1, row: 1, col: 4 }
			],
			[
				{ letter: 'T', base: 1, letterMult: 1, wordMult: 1, row: 2, col: 0 },
				{ letter: 'E', base: 1, letterMult: 1, wordMult: 1, row: 2, col: 1 },
				{ letter: 'S', base: 1, letterMult: 1, wordMult: 1, row: 2, col: 2 },
				{ letter: 'T', base: 1, letterMult: 1, wordMult: 1, row: 2, col: 3 },
				{ letter: 'S', base: 1, letterMult: 1, wordMult: 1, row: 2, col: 4 }
			],
			[
				{ letter: 'A', base: 1, letterMult: 1, wordMult: 1, row: 3, col: 0 },
				{ letter: 'B', base: 3, letterMult: 1, wordMult: 1, row: 3, col: 1 },
				{ letter: 'C', base: 3, letterMult: 1, wordMult: 1, row: 3, col: 2 },
				{ letter: 'D', base: 2, letterMult: 1, wordMult: 1, row: 3, col: 3 },
				{ letter: 'E', base: 1, letterMult: 1, wordMult: 1, row: 3, col: 4 }
			],
			[
				{ letter: 'F', base: 4, letterMult: 1, wordMult: 1, row: 4, col: 0 },
				{ letter: 'G', base: 2, letterMult: 1, wordMult: 1, row: 4, col: 1 },
				{ letter: 'H', base: 4, letterMult: 1, wordMult: 1, row: 4, col: 2 },
				{ letter: 'I', base: 1, letterMult: 1, wordMult: 1, row: 4, col: 3 },
				{ letter: 'J', base: 8, letterMult: 1, wordMult: 1, row: 4, col: 4 }
			]
		];

		// Create a mock board state after the first move (tiles have fallen down and new ones generated)
		const boardAfterMove1: Tile[][] = [
			[
				{ letter: 'X', base: 8, letterMult: 1, wordMult: 1, row: 0, col: 0 }, // New tile
				{ letter: 'Y', base: 4, letterMult: 1, wordMult: 1, row: 0, col: 1 }, // New tile
				{ letter: 'Z', base: 10, letterMult: 1, wordMult: 1, row: 0, col: 2 }, // New tile
				{ letter: 'Q', base: 10, letterMult: 1, wordMult: 1, row: 0, col: 3 }, // New tile
				{ letter: 'K', base: 5, letterMult: 1, wordMult: 1, row: 0, col: 4 } // New tile
			],
			[
				{ letter: 'W', base: 4, letterMult: 1, wordMult: 1, row: 1, col: 0 }, // Fell down
				{ letter: 'O', base: 1, letterMult: 1, wordMult: 1, row: 1, col: 1 }, // Fell down
				{ letter: 'R', base: 1, letterMult: 1, wordMult: 1, row: 1, col: 2 }, // Fell down
				{ letter: 'L', base: 1, letterMult: 1, wordMult: 1, row: 1, col: 3 }, // Fell down
				{ letter: 'D', base: 2, letterMult: 1, wordMult: 1, row: 1, col: 4 } // Fell down
			],
			[
				{ letter: 'T', base: 1, letterMult: 1, wordMult: 1, row: 2, col: 0 },
				{ letter: 'E', base: 1, letterMult: 1, wordMult: 1, row: 2, col: 1 },
				{ letter: 'S', base: 1, letterMult: 1, wordMult: 1, row: 2, col: 2 },
				{ letter: 'T', base: 1, letterMult: 1, wordMult: 1, row: 2, col: 3 },
				{ letter: 'S', base: 1, letterMult: 1, wordMult: 1, row: 2, col: 4 }
			],
			[
				{ letter: 'A', base: 1, letterMult: 1, wordMult: 1, row: 3, col: 0 },
				{ letter: 'B', base: 3, letterMult: 1, wordMult: 1, row: 3, col: 1 },
				{ letter: 'C', base: 3, letterMult: 1, wordMult: 1, row: 3, col: 2 },
				{ letter: 'D', base: 2, letterMult: 1, wordMult: 1, row: 3, col: 3 },
				{ letter: 'E', base: 1, letterMult: 1, wordMult: 1, row: 3, col: 4 }
			],
			[
				{ letter: 'F', base: 4, letterMult: 1, wordMult: 1, row: 4, col: 0 },
				{ letter: 'G', base: 2, letterMult: 1, wordMult: 1, row: 4, col: 1 },
				{ letter: 'H', base: 4, letterMult: 1, wordMult: 1, row: 4, col: 2 },
				{ letter: 'I', base: 1, letterMult: 1, wordMult: 1, row: 4, col: 3 },
				{ letter: 'J', base: 8, letterMult: 1, wordMult: 1, row: 4, col: 4 }
			]
		];

		// Create mock moves
		mockMoves = [
			{
				word: 'HELLO',
				score: 8,
				positions: [
					[0, 0],
					[0, 1],
					[0, 2],
					[0, 3],
					[0, 4]
				], // Top row
				boardBefore: mockInitialBoard,
				boardAfter: boardAfterMove1
			}
		];
	});

	describe('displayBoard logic', () => {
		it('should show board state before move with selected positions highlighted', () => {
			// Simulate the NEW displayBoard logic from GameReplayViewer
			const currentMoveIndex = 0; // Viewing first move
			const move = mockMoves[0];

			// New approach: show boardBefore for the move being viewed
			const displayBoard = move.boardBefore!;

			// Verify that ALL positions show original letters (from boardBefore)
			expect(displayBoard[0][0].letter).toBe('H'); // Original letter from boardBefore
			expect(displayBoard[0][1].letter).toBe('E'); // Original letter from boardBefore
			expect(displayBoard[0][2].letter).toBe('L'); // Original letter from boardBefore
			expect(displayBoard[0][3].letter).toBe('L'); // Original letter from boardBefore
			expect(displayBoard[0][4].letter).toBe('O'); // Original letter from boardBefore

			// Non-selected positions also show original letters (no position changes due to gravity)
			expect(displayBoard[1][0].letter).toBe('W'); // Original position from boardBefore
			expect(displayBoard[2][0].letter).toBe('T'); // Original position from boardBefore
			expect(displayBoard[4][4].letter).toBe('J'); // Original position from boardBefore

			// The highlighting of selected positions is handled by the BoardVisualization component
			// based on the selectedPositions prop, not by changing the board data
		});

		it('should correctly handle multiple moves with proper board states', () => {
			// Create a second move to test the issue described
			const boardAfterMove2: Tile[][] = [
				[
					{ letter: 'P', base: 3, letterMult: 1, wordMult: 1, row: 0, col: 0 }, // Different from move 1
					{ letter: 'Q', base: 10, letterMult: 1, wordMult: 1, row: 0, col: 1 },
					{ letter: 'R', base: 1, letterMult: 1, wordMult: 1, row: 0, col: 2 },
					{ letter: 'S', base: 1, letterMult: 1, wordMult: 1, row: 0, col: 3 },
					{ letter: 'T', base: 1, letterMult: 1, wordMult: 1, row: 0, col: 4 }
				],
				// ... rest of board
				...mockMoves[0].boardAfter!.slice(1)
			];

			const move2: EnhancedMove = {
				word: 'WORLD',
				score: 9,
				positions: [
					[1, 0],
					[1, 1],
					[1, 2],
					[1, 3],
					[1, 4]
				], // Second row
				boardBefore: mockMoves[0].boardAfter!, // Board after move 1
				boardAfter: boardAfterMove2
			};

			const movesWithTwo = [mockMoves[0], move2];

			// Test viewing move 1 (index 0)
			const currentMoveIndex = 0;
			const move = movesWithTwo[0];
			const currentBoard = move.boardAfter!; // Should be board after move 1, NOT move 2

			// Verify non-selected positions show board state after move 1, not move 2
			expect(currentBoard[0][0].letter).toBe('X'); // From move 1, not 'P' from move 2
			expect(currentBoard[0][1].letter).toBe('Y'); // From move 1, not 'Q' from move 2
		});

		it('should show boardBefore state to avoid position changes from gravity', () => {
			// This test verifies the fix for the issue where non-selected tiles
			// were changing position due to gravity effects from selected tiles being removed

			const currentMoveIndex = 0; // Viewing first move
			const move = mockMoves[0];

			// New approach: simply show boardBefore for the move being viewed
			const displayBoard = move.boardBefore!;

			// ALL tiles should show their original positions from boardBefore
			// This prevents the confusing position changes that happen due to gravity

			// Selected positions show original letters (these will be highlighted by UI)
			expect(displayBoard[0][0].letter).toBe('H'); // Original from boardBefore
			expect(displayBoard[0][1].letter).toBe('E'); // Original from boardBefore
			expect(displayBoard[0][2].letter).toBe('L'); // Original from boardBefore
			expect(displayBoard[0][3].letter).toBe('L'); // Original from boardBefore
			expect(displayBoard[0][4].letter).toBe('O'); // Original from boardBefore

			// Non-selected positions also show original letters (no gravity effects)
			expect(displayBoard[1][0].letter).toBe('W'); // Original position, not affected by gravity
			expect(displayBoard[1][1].letter).toBe('O'); // Original position, not affected by gravity
			expect(displayBoard[2][0].letter).toBe('T'); // Original position, not affected by gravity

			// This approach eliminates the bug where tiles like the top-left 'F'
			// would incorrectly change to 'S' due to gravity effects
		});

		it('should return initial board when not viewing a specific move', () => {
			const currentMoveIndex = -1; // Initial state

			// When currentMoveIndex is -1, should return initial board
			const displayBoard = mockInitialBoard;

			expect(displayBoard).toBe(mockInitialBoard);
		});

		it('should handle missing boardBefore data gracefully', () => {
			const moveWithoutBoardBefore: EnhancedMove = {
				word: 'TEST',
				score: 4,
				positions: [
					[0, 0],
					[0, 1],
					[0, 2],
					[0, 3]
				],
				// No boardBefore data
				boardAfter: mockMoves[0].boardAfter
			};

			// When boardBefore is missing, the component should return an empty array
			// or fall back gracefully (this is handled by the || [] in the component)
			const displayBoard = moveWithoutBoardBefore.boardBefore || [];

			expect(displayBoard).toEqual([]);
		});
	});

	describe('replay navigation', () => {
		it('should correctly identify selected positions for highlighting', () => {
			const move = mockMoves[0];
			const selectedPositions = move.positions;

			expect(selectedPositions).toEqual([
				[0, 0],
				[0, 1],
				[0, 2],
				[0, 3],
				[0, 4]
			]);
		});

		it('should show correct move information', () => {
			const move = mockMoves[0];

			expect(move.word).toBe('HELLO');
			expect(move.score).toBe(8);
			expect(move.positions).toHaveLength(5);
		});
	});
});
