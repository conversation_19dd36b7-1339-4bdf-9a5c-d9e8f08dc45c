<!--
  MoveAnalytics Component
  
  Displays detailed analytics and statistics for game moves:
  - Move efficiency analysis
  - Scoring breakdown
  - Multiplier usage statistics
  - Word length distribution
  - Performance metrics
-->

<script lang="ts">
	import type { EnhancedMove } from '$lib/types';

	interface Props {
		/** Array of enhanced moves to analyze */
		moves: EnhancedMove[];
		/** Per-round greedy moves for comparison */
		greedyMoves?: Array<{ word: string; score: number }>;
	}

	let { moves, greedyMoves = [] }: Props = $props();

	// Calculate analytics
	let analytics = $derived(() => {
		const totalScore = moves.reduce((sum, move) => sum + move.score, 0);
		const greedyTotal = greedyMoves.reduce((sum, move) => sum + move.score, 0);

		// Word length distribution
		const wordLengths = moves.map((move) => move.word.length);
		const avgWordLength = wordLengths.reduce((sum, len) => sum + len, 0) / wordLengths.length;
		const minWordLength = Math.min(...wordLengths);
		const maxWordLength = Math.max(...wordLengths);

		// Score distribution
		const scores = moves.map((move) => move.score);
		const avgScore = scores.reduce((sum, score) => sum + score, 0) / scores.length;
		const minScore = Math.min(...scores);
		const maxScore = Math.max(...scores);

		// Multiplier usage analysis
		let letterMultiplierUsage = 0;
		let wordMultiplierUsage = 0;
		let totalMultiplierValue = 0;

		moves.forEach((move) => {
			if (move.boardBefore) {
				move.positions.forEach(([row, col]) => {
					const tile = move.boardBefore![row][col];
					if (tile.letterMult > 1) {
						letterMultiplierUsage++;
						totalMultiplierValue += tile.letterMult - 1;
					}
					if (tile.wordMult > 1) {
						wordMultiplierUsage++;
						totalMultiplierValue += tile.wordMult - 1;
					}
				});
			}
		});

		// Efficiency metrics
		const improvement = totalScore - greedyTotal;
		const improvementPercentage = greedyTotal > 0 ? (improvement / greedyTotal) * 100 : 0;

		return {
			totalScore,
			greedyTotal,
			improvement,
			improvementPercentage,
			avgWordLength: Number(avgWordLength.toFixed(1)),
			minWordLength,
			maxWordLength,
			avgScore: Number(avgScore.toFixed(1)),
			minScore,
			maxScore,
			letterMultiplierUsage,
			wordMultiplierUsage,
			totalMultiplierValue,
			moveCount: moves.length
		};
	});

	// Word length distribution for chart
	let wordLengthDistribution = $derived(() => {
		const distribution: Record<number, number> = {};
		moves.forEach((move) => {
			const length = move.word.length;
			distribution[length] = (distribution[length] || 0) + 1;
		});
		return Object.entries(distribution)
			.map(([length, count]) => ({ length: Number(length), count }))
			.sort((a, b) => a.length - b.length);
	});

	// Score distribution for chart
	let scoreRanges = $derived(() => {
		const ranges = [
			{ min: 0, max: 20, label: '0-20', count: 0 },
			{ min: 21, max: 40, label: '21-40', count: 0 },
			{ min: 41, max: 60, label: '41-60', count: 0 },
			{ min: 61, max: 80, label: '61-80', count: 0 },
			{ min: 81, max: 100, label: '81-100', count: 0 },
			{ min: 101, max: Infinity, label: '100+', count: 0 }
		];

		moves.forEach((move) => {
			const range = ranges.find((r) => move.score >= r.min && move.score <= r.max);
			if (range) range.count++;
		});

		return ranges.filter((range) => range.count > 0);
	});

	// Format percentage
	function formatPercentage(value: number): string {
		return `${value >= 0 ? '+' : ''}${value.toFixed(1)}%`;
	}

	// Format number with commas
	function formatNumber(value: number): string {
		return value.toLocaleString();
	}
</script>

<div class="move-analytics rounded-lg bg-white p-6 shadow-lg">
	<!-- Header -->
	<div class="mb-6">
		<h3 class="mb-2 text-xl font-semibold text-gray-900">Move Analytics</h3>
		<p class="text-gray-600">Detailed analysis of {analytics().moveCount} moves</p>
	</div>

	<!-- Key Metrics Grid -->
	<div class="mb-8 grid grid-cols-2 gap-4 md:grid-cols-4">
		<!-- Total Score -->
		<div class="rounded-lg bg-blue-50 p-4 text-center">
			<div class="text-2xl font-bold text-blue-600">{formatNumber(analytics().totalScore)}</div>
			<div class="text-sm text-gray-600">Total Score</div>
		</div>

		<!-- Improvement -->
		<div class="rounded-lg bg-green-50 p-4 text-center">
			<div class="text-2xl font-bold text-green-600">
				{analytics().improvement >= 0 ? '+' : ''}{formatNumber(analytics().improvement)}
			</div>
			<div class="text-sm text-gray-600">vs. Greedy</div>
		</div>

		<!-- Average Score -->
		<div class="rounded-lg bg-purple-50 p-4 text-center">
			<div class="text-2xl font-bold text-purple-600">{analytics().avgScore}</div>
			<div class="text-sm text-gray-600">Avg Score</div>
		</div>

		<!-- Efficiency -->
		<div class="rounded-lg bg-orange-50 p-4 text-center">
			<div class="text-2xl font-bold text-orange-600">
				{formatPercentage(analytics().improvementPercentage)}
			</div>
			<div class="text-sm text-gray-600">Efficiency</div>
		</div>
	</div>

	<!-- Detailed Statistics -->
	<div class="mb-6 grid grid-cols-1 gap-6 md:grid-cols-2">
		<!-- Word Statistics -->
		<div class="rounded-lg border border-gray-200 p-4">
			<h4 class="mb-3 font-semibold text-gray-900">Word Statistics</h4>
			<div class="space-y-2 text-sm">
				<div class="flex justify-between">
					<span class="text-gray-600">Average Length:</span>
					<span class="font-medium">{analytics().avgWordLength} letters</span>
				</div>
				<div class="flex justify-between">
					<span class="text-gray-600">Length Range:</span>
					<span class="font-medium"
						>{analytics().minWordLength} - {analytics().maxWordLength} letters</span
					>
				</div>
				<div class="flex justify-between">
					<span class="text-gray-600">Total Letters:</span>
					<span class="font-medium">{moves.reduce((sum, move) => sum + move.word.length, 0)}</span>
				</div>
			</div>
		</div>

		<!-- Score Statistics -->
		<div class="rounded-lg border border-gray-200 p-4">
			<h4 class="mb-3 font-semibold text-gray-900">Score Statistics</h4>
			<div class="space-y-2 text-sm">
				<div class="flex justify-between">
					<span class="text-gray-600">Average Score:</span>
					<span class="font-medium">{analytics().avgScore} points</span>
				</div>
				<div class="flex justify-between">
					<span class="text-gray-600">Score Range:</span>
					<span class="font-medium">{analytics().minScore} - {analytics().maxScore} points</span>
				</div>
				<div class="flex justify-between">
					<span class="text-gray-600">Best Move:</span>
					<span class="font-medium"
						>{moves.find((m) => m.score === analytics().maxScore)?.word || 'N/A'}</span
					>
				</div>
			</div>
		</div>
	</div>

	<!-- Multiplier Usage -->
	<div class="mb-6 rounded-lg border border-gray-200 p-4">
		<h4 class="mb-3 font-semibold text-gray-900">Multiplier Usage</h4>
		<div class="grid grid-cols-3 gap-4 text-center">
			<div>
				<div class="text-xl font-bold text-orange-600">{analytics().letterMultiplierUsage}</div>
				<div class="text-sm text-gray-600">Letter Multipliers</div>
			</div>
			<div>
				<div class="text-xl font-bold text-purple-600">{analytics().wordMultiplierUsage}</div>
				<div class="text-sm text-gray-600">Word Multipliers</div>
			</div>
			<div>
				<div class="text-xl font-bold text-blue-600">{analytics().totalMultiplierValue}</div>
				<div class="text-sm text-gray-600">Total Bonus Value</div>
			</div>
		</div>
	</div>

	<!-- Word Length Distribution -->
	{#if wordLengthDistribution().length > 0}
		<div class="mb-6 rounded-lg border border-gray-200 p-4">
			<h4 class="mb-3 font-semibold text-gray-900">Word Length Distribution</h4>
			<div class="space-y-2">
				{#each wordLengthDistribution() as { length, count }}
					<div class="flex items-center space-x-3">
						<span class="w-16 text-sm text-gray-600">{length} letters:</span>
						<div class="h-2 flex-1 rounded-full bg-gray-200">
							<div
								class="h-2 rounded-full bg-blue-600 transition-all duration-300"
								style="width: {(count / analytics().moveCount) * 100}%"
							></div>
						</div>
						<span class="w-8 text-sm font-medium">{count}</span>
					</div>
				{/each}
			</div>
		</div>
	{/if}

	<!-- Score Distribution -->
	{#if scoreRanges().length > 0}
		<div class="rounded-lg border border-gray-200 p-4">
			<h4 class="mb-3 font-semibold text-gray-900">Score Distribution</h4>
			<div class="space-y-2">
				{#each scoreRanges() as range}
					<div class="flex items-center space-x-3">
						<span class="w-16 text-sm text-gray-600">{range.label}:</span>
						<div class="h-2 flex-1 rounded-full bg-gray-200">
							<div
								class="h-2 rounded-full bg-green-600 transition-all duration-300"
								style="width: {(range.count / analytics().moveCount) * 100}%"
							></div>
						</div>
						<span class="w-8 text-sm font-medium">{range.count}</span>
					</div>
				{/each}
			</div>
		</div>
	{/if}

	<!-- Move List -->
	<div class="mt-6 rounded-lg border border-gray-200 p-4">
		<h4 class="mb-3 font-semibold text-gray-900">All Moves</h4>
		<div class="grid grid-cols-1 gap-2 sm:grid-cols-2 lg:grid-cols-3">
			{#each moves as move}
				<div class="flex items-center justify-between rounded bg-gray-50 p-2 text-sm">
					<span class="font-mono font-medium">{move.word}</span>
					<span class="text-gray-600">{move.score} pts</span>
				</div>
			{/each}
		</div>
	</div>
</div>
