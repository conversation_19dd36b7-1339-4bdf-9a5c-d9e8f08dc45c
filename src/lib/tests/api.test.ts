/**
 * Integration tests for API endpoints
 *
 * These tests verify the webhook and board result endpoints work correctly
 * with mock data and proper error handling.
 */

import { describe, it, expect, beforeEach, vi } from 'vitest';
import type { BestLineResult } from '$lib/types';

// Mock the database functions
const mockGetBestLine = vi.fn();
const mockInsertBestLine = vi.fn();

vi.mock('$lib/db', () => ({
	getBestLine: mockGetBestLine,
	insertBestLine: mockInsertBestLine
}));

// Mock the solver function
const mockSolveDailyBoard = vi.fn();

vi.mock('$lib/solver', () => ({
	solveDailyBoard: mockSolveDailyBoard
}));

// Test data
const mockBestLineResult: BestLineResult = {
	total: 150,
	words: ['HELLO', 'WORLD', 'TEST', 'GAME', 'SOLVE'],
	perRound: [
		{ word: 'HELLO', score: 25 },
		{ word: 'WORLD', score: 30 },
		{ word: 'TEST', score: 20 },
		{ word: 'GAME', score: 22 },
		{ word: 'SOLVE', score: 28 }
	]
};

// Mock platform environment
const mockPlatform = {
	env: {
		WEBHOOK_SECRET: 'test-secret-key',
		DB: {
			prepare: vi.fn().mockReturnValue({
				bind: vi.fn().mockReturnValue({
					first: vi.fn(),
					all: vi.fn(),
					run: vi.fn()
				})
			})
		},
		BROWSER: {
			launch: vi.fn().mockResolvedValue({
				newPage: vi.fn().mockResolvedValue({
					goto: vi.fn(),
					close: vi.fn()
				}),
				close: vi.fn()
			})
		}
	}
};

describe('API Endpoints', () => {
	beforeEach(() => {
		vi.clearAllMocks();
	});

	describe('/api/run webhook', () => {
		it('should reject requests without authentication', async () => {
			const request = new Request('http://localhost/api/run', {
				method: 'POST',
				headers: { 'Content-Type': 'application/json' }
			});

			// Import the handler dynamically to avoid module loading issues
			const { POST } = await import('../../routes/api/run/+server');
			const response = await POST({ request, platform: mockPlatform });

			expect(response.status).toBe(401);
			const data = await response.json();
			expect(data.success).toBe(false);
			expect(data.error).toBe('Unauthorized');
		});

		it('should accept requests with valid authentication', async () => {
			mockGetBestLine.mockResolvedValue(null); // No existing result
			mockSolveDailyBoard.mockResolvedValue(mockBestLineResult);

			const request = new Request('http://localhost/api/run', {
				method: 'POST',
				headers: {
					'Content-Type': 'application/json',
					Authorization: 'Bearer test-secret-key'
				},
				body: JSON.stringify({ date: '2025-06-17' })
			});

			const { POST } = await import('../../routes/api/run/+server');
			const response = await POST({ request, platform: mockPlatform });

			expect(response.status).toBe(202);
			const data = await response.json();
			expect(data.success).toBe(true);
			expect(data.message).toBe('Solver job started');
		});

		it('should return existing result if not forced', async () => {
			mockGetBestLine.mockResolvedValue(mockBestLineResult);

			const request = new Request('http://localhost/api/run', {
				method: 'POST',
				headers: {
					'Content-Type': 'application/json',
					Authorization: 'test-secret-key'
				},
				body: JSON.stringify({ date: '2025-06-17', force: false })
			});

			const { POST } = await import('../../routes/api/run/+server');
			const response = await POST({ request, platform: mockPlatform });

			expect(response.status).toBe(200);
			const data = await response.json();
			expect(data.success).toBe(true);
			expect(data.data.alreadyExists).toBe(true);
		});

		it('should validate request body format', async () => {
			const request = new Request('http://localhost/api/run', {
				method: 'POST',
				headers: {
					'Content-Type': 'application/json',
					Authorization: 'test-secret-key'
				},
				body: JSON.stringify({ date: 'invalid-date' })
			});

			const { POST } = await import('../../routes/api/run/+server');
			const response = await POST({ request, platform: mockPlatform });

			expect(response.status).toBe(400);
			const data = await response.json();
			expect(data.success).toBe(false);
			expect(data.message).toContain('Invalid date format');
		});

		it('should handle missing database service', async () => {
			const platformWithoutDB = {
				env: {
					WEBHOOK_SECRET: 'test-secret-key',
					BROWSER: mockPlatform.env.BROWSER
				}
			};

			const request = new Request('http://localhost/api/run', {
				method: 'POST',
				headers: {
					'Content-Type': 'application/json',
					Authorization: 'test-secret-key'
				}
			});

			const { POST } = await import('../../routes/api/run/+server');
			const response = await POST({ request, platform: platformWithoutDB });

			expect(response.status).toBe(503);
			const data = await response.json();
			expect(data.success).toBe(false);
			expect(data.message).toBe('D1 database not available');
		});
	});

	describe('/board/[date] endpoint', () => {
		it('should return board result for valid date', async () => {
			mockGetBestLine.mockResolvedValue(mockBestLineResult);

			const request = new Request('http://localhost/board/2025-06-17');
			const params = { date: '2025-06-17' };
			const url = new URL('http://localhost/board/2025-06-17');

			const { GET } = await import('../../routes/board/[date]/+server');
			const response = await GET({ request, params, url, platform: mockPlatform });

			expect(response.status).toBe(200);
			const data = await response.json();
			expect(data.success).toBe(true);
			expect(data.data.date).toBe('2025-06-17');
			expect(data.data.total).toBe(150);
			expect(data.data.words).toEqual(['HELLO', 'WORLD', 'TEST', 'GAME', 'SOLVE']);
		});

		it('should return 404 for non-existent date', async () => {
			mockGetBestLine.mockResolvedValue(null);

			const request = new Request('http://localhost/board/2025-06-17');
			const params = { date: '2025-06-17' };
			const url = new URL('http://localhost/board/2025-06-17');

			const { GET } = await import('../../routes/board/[date]/+server');
			const response = await GET({ request, params, url, platform: mockPlatform });

			expect(response.status).toBe(404);
			const data = await response.json();
			expect(data.success).toBe(false);
			expect(data.error).toBe('Not Found');
		});

		it('should validate date format', async () => {
			const request = new Request('http://localhost/board/invalid-date');
			const params = { date: 'invalid-date' };
			const url = new URL('http://localhost/board/invalid-date');

			const { GET } = await import('../../routes/board/[date]/+server');
			const response = await GET({ request, params, url, platform: mockPlatform });

			expect(response.status).toBe(400);
			const data = await response.json();
			expect(data.success).toBe(false);
			expect(data.message).toContain('Invalid date format');
		});

		it('should reject future dates', async () => {
			const futureDate = new Date();
			futureDate.setDate(futureDate.getDate() + 1);
			const futureDateStr = futureDate.toISOString().split('T')[0];

			const request = new Request(`http://localhost/board/${futureDateStr}`);
			const params = { date: futureDateStr };
			const url = new URL(`http://localhost/board/${futureDateStr}`);

			const { GET } = await import('../../routes/board/[date]/+server');
			const response = await GET({ request, params, url, platform: mockPlatform });

			expect(response.status).toBe(400);
			const data = await response.json();
			expect(data.success).toBe(false);
			expect(data.message).toContain('Cannot retrieve results for future dates');
		});

		it('should support summary format', async () => {
			mockGetBestLine.mockResolvedValue(mockBestLineResult);

			const request = new Request('http://localhost/board/2025-06-17?format=summary');
			const params = { date: '2025-06-17' };
			const url = new URL('http://localhost/board/2025-06-17?format=summary');

			const { GET } = await import('../../routes/board/[date]/+server');
			const response = await GET({ request, params, url, platform: mockPlatform });

			expect(response.status).toBe(200);
			const data = await response.json();
			expect(data.success).toBe(true);
			expect(data.data).toHaveProperty('total');
			expect(data.data).toHaveProperty('words');
			expect(data.data).toHaveProperty('wordCount');
			expect(data.data).not.toHaveProperty('perRound');
		});

		it('should handle database errors gracefully', async () => {
			mockGetBestLine.mockRejectedValue(new Error('Database connection failed'));

			const request = new Request('http://localhost/board/2025-06-17');
			const params = { date: '2025-06-17' };
			const url = new URL('http://localhost/board/2025-06-17');

			const { GET } = await import('../../routes/board/[date]/+server');
			const response = await GET({ request, params, url, platform: mockPlatform });

			expect(response.status).toBe(500);
			const data = await response.json();
			expect(data.success).toBe(false);
			expect(data.message).toBe('Failed to retrieve result from database');
		});
	});
});
