/**
 * Test for position fixing functionality
 * 
 * This test verifies that the position fixing logic correctly handles
 * mismatched positions in replay data.
 */

import { describe, it, expect } from 'vitest';

describe('Position Fix Validation', () => {
	it('should detect position mismatches correctly', () => {
		// Simulate the problematic data from the database
		const word = 'MAFFICKED';
		const positions: Array<[number, number]> = [[1,1],[2,0],[4,0],[2,3],[1,3],[4,2],[0,3],[1,2],[1,4]];
		
		// Simulate the board state from the database
		const boardBefore = [
			[
				{letter: "I", row: 0, col: 0},
				{letter: "E", row: 0, col: 1},
				{letter: "N", row: 0, col: 2},
				{letter: "I", row: 0, col: 3},
				{letter: "N", row: 0, col: 4}
			],
			[
				{letter: "D", row: 1, col: 0},
				{letter: "M", row: 1, col: 1},
				{letter: "Y", row: 1, col: 2},
				{letter: "N", row: 1, col: 3},
				{letter: "A", row: 1, col: 4}
			],
			[
				{letter: "I", row: 2, col: 0},
				{letter: "N", row: 2, col: 1},
				{letter: "D", row: 2, col: 2},
				{letter: "P", row: 2, col: 3},
				{letter: "N", row: 2, col: 4}
			],
			[
				{letter: "W", row: 3, col: 0},
				{letter: "P", row: 3, col: 1},
				{letter: "D", row: 3, col: 2},
				{letter: "W", row: 3, col: 3},
				{letter: "T", row: 3, col: 4}
			],
			[
				{letter: "F", row: 4, col: 0},
				{letter: "W", row: 4, col: 1},
				{letter: "C", row: 4, col: 2},
				{letter: "G", row: 4, col: 3},
				{letter: "R", row: 4, col: 4}
			]
		];

		// Extract letters using the positions
		const extractedLetters = positions
			.map(([row, col]) => {
				const tile = boardBefore[row]?.[col];
				return tile ? tile.letter : '?';
			})
			.join('');

		// This should demonstrate the mismatch
		expect(extractedLetters).not.toBe(word);
		expect(extractedLetters).toBe('MIFPNCIYA'); // What we actually get
		expect(word).toBe('MAFFICKED'); // What we expect

		console.log(`Position mismatch detected: word="${word}", extracted="${extractedLetters}"`);
	});

	it('should be able to find correct positions for MAFFICKED', () => {
		const word = 'MAFFICKED';
		
		// Same board state
		const boardBefore = [
			[
				{letter: "I", row: 0, col: 0, letterMult: 1, wordMult: 1},
				{letter: "E", row: 0, col: 1, letterMult: 1, wordMult: 1},
				{letter: "N", row: 0, col: 2, letterMult: 1, wordMult: 1},
				{letter: "I", row: 0, col: 3, letterMult: 1, wordMult: 1},
				{letter: "N", row: 0, col: 4, letterMult: 1, wordMult: 1}
			],
			[
				{letter: "D", row: 1, col: 0, letterMult: 1, wordMult: 1},
				{letter: "M", row: 1, col: 1, letterMult: 1, wordMult: 1},
				{letter: "Y", row: 1, col: 2, letterMult: 1, wordMult: 1},
				{letter: "N", row: 1, col: 3, letterMult: 1, wordMult: 2},
				{letter: "A", row: 1, col: 4, letterMult: 1, wordMult: 3}
			],
			[
				{letter: "I", row: 2, col: 0, letterMult: 1, wordMult: 1},
				{letter: "N", row: 2, col: 1, letterMult: 1, wordMult: 1},
				{letter: "D", row: 2, col: 2, letterMult: 1, wordMult: 1},
				{letter: "P", row: 2, col: 3, letterMult: 2, wordMult: 1},
				{letter: "N", row: 2, col: 4, letterMult: 1, wordMult: 1}
			],
			[
				{letter: "W", row: 3, col: 0, letterMult: 1, wordMult: 1},
				{letter: "P", row: 3, col: 1, letterMult: 1, wordMult: 1},
				{letter: "D", row: 3, col: 2, letterMult: 1, wordMult: 1},
				{letter: "W", row: 3, col: 3, letterMult: 1, wordMult: 1},
				{letter: "T", row: 3, col: 4, letterMult: 1, wordMult: 1}
			],
			[
				{letter: "F", row: 4, col: 0, letterMult: 1, wordMult: 3},
				{letter: "W", row: 4, col: 1, letterMult: 1, wordMult: 1},
				{letter: "C", row: 4, col: 2, letterMult: 1, wordMult: 1},
				{letter: "G", row: 4, col: 3, letterMult: 3, wordMult: 1},
				{letter: "R", row: 4, col: 4, letterMult: 1, wordMult: 1}
			]
		];

		// Function to find correct positions (copied from the fix)
		function findCorrectPositions(word: string, board: any[][]): Array<[number, number]> | null {
			const letters = word.split('');
			const positions: Array<[number, number]> = [];
			const usedPositions = new Set<string>();

			// Build a map of available tiles for each letter
			const letterTileMap: Record<string, Array<{ row: number; col: number; tile: any }>> = {};
			for (let row = 0; row < 5; row++) {
				for (let col = 0; col < 5; col++) {
					const tile = board[row][col];
					if (tile && tile.letter) {
						if (!letterTileMap[tile.letter]) {
							letterTileMap[tile.letter] = [];
						}
						letterTileMap[tile.letter].push({ row, col, tile });
					}
				}
			}

			// Sort tiles by multiplier value (best first) for each letter
			for (const letter in letterTileMap) {
				letterTileMap[letter].sort((a, b) => {
					const aValue = (a.tile.letterMult || 1) * (a.tile.wordMult || 1);
					const bValue = (b.tile.letterMult || 1) * (b.tile.wordMult || 1);
					return bValue - aValue; // Descending order (best first)
				});
			}

			// For each letter in the word (in order), find the best available tile
			for (let i = 0; i < letters.length; i++) {
				const letter = letters[i];
				const availableTiles = letterTileMap[letter];
				if (!availableTiles || availableTiles.length === 0) {
					return null; // Letter not available on board
				}

				// Find the best unused tile for this letter
				let bestTile: { row: number; col: number; tile: any } | null = null;
				for (const tileInfo of availableTiles) {
					const posKey = `${tileInfo.row},${tileInfo.col}`;
					if (!usedPositions.has(posKey)) {
						bestTile = tileInfo;
						break; // Take the first (best) available tile
					}
				}

				if (!bestTile) {
					return null; // No unused tiles available for this letter
				}

				positions.push([bestTile.row, bestTile.col]);
				usedPositions.add(`${bestTile.row},${bestTile.col}`);
			}

			return positions;
		}

		const correctedPositions = findCorrectPositions(word, boardBefore);
		expect(correctedPositions).not.toBeNull();

		if (correctedPositions) {
			// Verify the corrected positions spell out the word correctly
			const correctedLetters = correctedPositions
				.map(([row, col]) => {
					const tile = boardBefore[row]?.[col];
					return tile ? tile.letter : '?';
				})
				.join('');

			expect(correctedLetters).toBe(word);
			console.log(`Corrected positions for "${word}":`, correctedPositions);
			console.log(`Corrected letters: "${correctedLetters}"`);
		}
	});
});
