/**
 * Tests for enhanced solution tracking functionality
 * 
 * This test suite verifies that the enhanced tracking correctly captures
 * tile positions, handles duplicate letters, and creates proper board states.
 */

import { describe, it, expect, beforeEach } from 'vitest';
import { Board } from '../models/Board';
import { Tile } from '../models/Tile';
import { Word } from '../models/Word';
import { GameState } from '../models/GameState';
import { wordToEnhancedMove, type EnhancedMove, type BestLineResult } from '../types';

describe('Enhanced Solution Tracking', () => {
	let testBoard: Board;
	let testWord: Word;

	beforeEach(() => {
		// Create a test board with known tiles
		const tiles: Tile[][] = [];
		for (let row = 0; row < 5; row++) {
			tiles[row] = [];
			for (let col = 0; col < 5; col++) {
				// Create tiles with predictable letters and some multipliers
				const letter = String.fromCharCode(65 + (row * 5 + col) % 26); // A-Z cycling
				const letterMult = (row === 0 && col === 0) ? 2 : 1; // Letter multiplier at [0,0]
				const wordMult = (row === 2 && col === 2) ? 3 : 1; // Word multiplier at [2,2]
				tiles[row][col] = new Tile(letter, row as 0|1|2|3|4, col as 0|1|2|3|4, letterMult as 1|2|3, wordMult as 1|2|3);
			}
		}
		testBoard = new Board(tiles);

		// Create a test word using positions [0,0], [0,1], [0,2] (A, B, C)
		testWord = new Word('ABC', [[0,0], [0,1], [0,2]]);
	});

	describe('wordToEnhancedMove', () => {
		it('should create enhanced move with correct word and positions', () => {
			const enhancedMove = wordToEnhancedMove(testWord);

			expect(enhancedMove.word).toBe('ABC');
			expect(enhancedMove.score).toBe(testWord.score);
			expect(enhancedMove.positions).toEqual([[0,0], [0,1], [0,2]]);
		});

		it('should include board states when provided', () => {
			const boardBefore = testBoard.tiles.map(row => row.map(tile => tile.toObject()));
			const boardAfter = testBoard.apply(testWord).tiles.map(row => row.map(tile => tile.toObject()));

			const enhancedMove = wordToEnhancedMove(testWord, boardBefore, boardAfter);

			expect(enhancedMove.boardBefore).toBeDefined();
			expect(enhancedMove.boardAfter).toBeDefined();
			expect(enhancedMove.boardBefore).toHaveLength(5);
			expect(enhancedMove.boardAfter).toHaveLength(5);
		});
	});

	describe('Board state capture', () => {
		it('should capture different board states before and after move', () => {
			const boardBefore = testBoard.tiles.map(row => row.map(tile => tile.toObject()));
			const newBoard = testBoard.apply(testWord);
			const boardAfter = newBoard.tiles.map(row => row.map(tile => tile.toObject()));

			// Board states should be different (tiles have moved/changed)
			expect(boardBefore).not.toEqual(boardAfter);

			// Verify that the used positions are different in the after state
			// (tiles should have fallen down and new tiles generated)
			const beforeTile00 = boardBefore[0][0];
			const afterTile00 = boardAfter[0][0];
			
			// The tile at [0,0] should be different after the move
			// (either a new random tile or a tile that fell from above)
			expect(beforeTile00.letter).not.toBe(afterTile00.letter);
		});

		it('should preserve tile positions and multipliers in board state', () => {
			const boardBefore = testBoard.tiles.map(row => row.map(tile => tile.toObject()));

			// Check that positions and multipliers are preserved
			expect(boardBefore[0][0].row).toBe(0);
			expect(boardBefore[0][0].col).toBe(0);
			expect(boardBefore[0][0].letterMult).toBe(2); // We set this in beforeEach
			expect(boardBefore[2][2].wordMult).toBe(3); // We set this in beforeEach
		});
	});

	describe('Duplicate letter handling', () => {
		it('should handle words with duplicate letters correctly', () => {
			// Create a word with duplicate letters: "AAB" using positions [0,0], [1,0], [0,1]
			// Note: This assumes the board has 'A' at both [0,0] and [1,0]
			const duplicateWord = new Word('AAB', [[0,0], [1,0], [0,1]]);
			const enhancedMove = wordToEnhancedMove(duplicateWord);

			expect(enhancedMove.word).toBe('AAB');
			expect(enhancedMove.positions).toEqual([[0,0], [1,0], [0,1]]);
			expect(enhancedMove.positions).toHaveLength(3);

			// Verify that each position is unique
			const positionStrings = enhancedMove.positions.map(([r, c]) => `${r},${c}`);
			const uniquePositions = new Set(positionStrings);
			expect(uniquePositions.size).toBe(3);
		});
	});

	describe('GameState integration', () => {
		it('should work with GameState playMove method', () => {
			const gameState = new GameState(testBoard);
			const newState = gameState.playMove(testWord);

			expect(newState.moves).toHaveLength(1);
			expect(newState.moves[0].letters).toBe('ABC');
			expect(newState.moves[0].positions).toEqual([[0,0], [0,1], [0,2]]);
			expect(newState.turn).toBe(1);
		});
	});

	describe('BestLineResult compatibility', () => {
		it('should maintain backward compatibility with existing BestLineResult', () => {
			// Test that a BestLineResult without enhancedMoves still works
			const basicResult: BestLineResult = {
				total: 100,
				words: ['ABC', 'DEF'],
				perRound: [
					{ word: 'ABC', score: 50 },
					{ word: 'DEF', score: 50 }
				]
			};

			expect(basicResult.enhancedMoves).toBeUndefined();
			expect(basicResult.enhancedPerRound).toBeUndefined();
		});

		it('should support enhanced BestLineResult with move data', () => {
			const enhancedMove1 = wordToEnhancedMove(testWord);
			const enhancedMove2 = wordToEnhancedMove(new Word('DEF', [[1,0], [1,1], [1,2]]));

			const enhancedResult: BestLineResult = {
				total: 100,
				words: ['ABC', 'DEF'],
				perRound: [
					{ word: 'ABC', score: 50 },
					{ word: 'DEF', score: 50 }
				],
				enhancedMoves: [enhancedMove1, enhancedMove2],
				enhancedPerRound: [enhancedMove1, enhancedMove2]
			};

			expect(enhancedResult.enhancedMoves).toHaveLength(2);
			expect(enhancedResult.enhancedMoves![0].positions).toEqual([[0,0], [0,1], [0,2]]);
			expect(enhancedResult.enhancedMoves![1].positions).toEqual([[1,0], [1,1], [1,2]]);
		});
	});

	describe('JSON serialization', () => {
		it('should serialize and deserialize enhanced move data correctly', () => {
			const enhancedMove = wordToEnhancedMove(
				testWord,
				testBoard.tiles.map(row => row.map(tile => tile.toObject())),
				testBoard.apply(testWord).tiles.map(row => row.map(tile => tile.toObject()))
			);

			const serialized = JSON.stringify(enhancedMove);
			const deserialized: EnhancedMove = JSON.parse(serialized);

			expect(deserialized.word).toBe(enhancedMove.word);
			expect(deserialized.score).toBe(enhancedMove.score);
			expect(deserialized.positions).toEqual(enhancedMove.positions);
			expect(deserialized.boardBefore).toEqual(enhancedMove.boardBefore);
			expect(deserialized.boardAfter).toEqual(enhancedMove.boardAfter);
		});
	});
});
