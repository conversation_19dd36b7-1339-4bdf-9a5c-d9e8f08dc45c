/**
 * Board class implementation for the Letters game
 *
 * Represents the 5x5 game board with tiles and provides methods for
 * scoring, applying moves, and state management.
 */

import { Tile } from './Tile';
import { GAME_CONFIG, type Board as BoardInterface, type Word, type Position } from '../types';

/**
 * Represents the 5x5 game board
 */
export class Board implements BoardInterface {
	public readonly tiles: Tile[][];

	constructor(tiles?: Tile[][]) {
		if (tiles) {
			// Validate dimensions
			if (tiles.length !== GAME_CONFIG.BOARD_SIZE) {
				throw new Error(`Board must have ${GAME_CONFIG.BOARD_SIZE} rows`);
			}
			for (let i = 0; i < GAME_CONFIG.BOARD_SIZE; i++) {
				if (tiles[i].length !== GAME_CONFIG.BOARD_SIZE) {
					throw new Error(`Board row ${i} must have ${GAME_CONFIG.BOARD_SIZE} columns`);
				}
			}
			this.tiles = tiles;
		} else {
			// Create empty board
			this.tiles = this.createEmptyGrid();
		}
	}

	/**
	 * Create an empty 5x5 grid
	 */
	private createEmptyGrid(): Tile[][] {
		const grid: Tile[][] = [];
		for (let row = 0; row < GAME_CONFIG.BOARD_SIZE; row++) {
			grid[row] = [];
			for (let col = 0; col < GAME_CONFIG.BOARD_SIZE; col++) {
				// Create placeholder tiles - in real game these would be populated from scraping
				grid[row][col] = new Tile('A', row as 0 | 1 | 2 | 3 | 4, col as 0 | 1 | 2 | 3 | 4);
			}
		}
		return grid;
	}

	/**
	 * Get tile at specific position
	 */
	getTile(row: number, col: number): Tile | null {
		if (row < 0 || row >= GAME_CONFIG.BOARD_SIZE || col < 0 || col >= GAME_CONFIG.BOARD_SIZE) {
			return null;
		}
		return this.tiles[row][col];
	}

	/**
	 * Get tile at position tuple
	 */
	getTileAt(position: Position): Tile | null {
		return this.getTile(position[0], position[1]);
	}

	/**
	 * Set tile at specific position
	 */
	setTile(row: number, col: number, tile: Tile): void {
		if (row < 0 || row >= GAME_CONFIG.BOARD_SIZE || col < 0 || col >= GAME_CONFIG.BOARD_SIZE) {
			throw new Error(`Position [${row}, ${col}] is out of bounds`);
		}
		this.tiles[row][col] = tile;
	}

	/**
	 * Calculate score for a word on this board
	 */
	score(word: Word): number {
		let totalScore = 0;
		let wordMultiplier = 1;

		// Calculate score for each letter position
		for (let i = 0; i < word.positions.length; i++) {
			const [row, col] = word.positions[i];
			const tile = this.getTile(row, col);

			if (!tile) {
				throw new Error(`Invalid position [${row}, ${col}] in word`);
			}

			// Verify the letter matches
			const expectedLetter = word.letters[i].toUpperCase();
			if (tile.letter !== expectedLetter) {
				throw new Error(
					`Letter mismatch at [${row}, ${col}]: expected ${expectedLetter}, got ${tile.letter}`
				);
			}

			// Calculate letter score with multiplier
			const letterScore = tile.getLetterScore();
			totalScore += letterScore;

			// Accumulate word multiplier
			wordMultiplier *= tile.wordMult;
		}

		// Apply word multiplier
		return totalScore * wordMultiplier;
	}

	/**
	 * Apply a word to the board (drops tiles and spawns replacements)
	 * Returns a new Board instance with the changes applied
	 */
	apply(word: Word): Board {
		const newTiles = this.clone().tiles;

		// Remove tiles used in the word (simulate dropping)
		const usedPositions = new Set(word.positions.map((pos) => `${pos[0]},${pos[1]}`));

		// Drop tiles down to fill gaps
		for (let col = 0; col < GAME_CONFIG.BOARD_SIZE; col++) {
			// Collect remaining tiles in this column
			const remainingTiles: Tile[] = [];

			for (let row = GAME_CONFIG.BOARD_SIZE - 1; row >= 0; row--) {
				const posKey = `${row},${col}`;
				if (!usedPositions.has(posKey)) {
					remainingTiles.push(newTiles[row][col]);
				}
			}

			// Fill column from bottom up with remaining tiles
			for (let row = GAME_CONFIG.BOARD_SIZE - 1; row >= 0; row--) {
				const remainingIndex = GAME_CONFIG.BOARD_SIZE - 1 - row;

				if (remainingIndex < remainingTiles.length) {
					// Use existing tile, but update its position to preserve multipliers at the target position
					const existingTile = remainingTiles[remainingIndex];
					const originalTileAtPosition = this.tiles[row][col];

					// Create new tile with existing letter but original position's multipliers
					newTiles[row][col] = new Tile(
						existingTile.letter,
						row as 0 | 1 | 2 | 3 | 4,
						col as 0 | 1 | 2 | 3 | 4,
						originalTileAtPosition.letterMult, // Preserve original multipliers
						originalTileAtPosition.wordMult // Preserve original multipliers
					);
				} else {
					// Generate new tile with random letter but preserve original position's multipliers
					const originalTileAtPosition = this.tiles[row][col];
					newTiles[row][col] = Tile.createRandomWithMultipliers(
						row as 0 | 1 | 2 | 3 | 4,
						col as 0 | 1 | 2 | 3 | 4,
						originalTileAtPosition.letterMult, // Preserve original multipliers
						originalTileAtPosition.wordMult // Preserve original multipliers
					);
				}
			}
		}

		return new Board(newTiles);
	}

	/**
	 * Create a deep copy of this board
	 */
	clone(): Board {
		const newTiles: Tile[][] = [];
		for (let row = 0; row < GAME_CONFIG.BOARD_SIZE; row++) {
			newTiles[row] = [];
			for (let col = 0; col < GAME_CONFIG.BOARD_SIZE; col++) {
				newTiles[row][col] = this.tiles[row][col].clone();
			}
		}
		return new Board(newTiles);
	}

	/**
	 * Get all tiles as a flat array
	 */
	getAllTiles(): Tile[] {
		const allTiles: Tile[] = [];
		for (let row = 0; row < GAME_CONFIG.BOARD_SIZE; row++) {
			for (let col = 0; col < GAME_CONFIG.BOARD_SIZE; col++) {
				allTiles.push(this.tiles[row][col]);
			}
		}
		return allTiles;
	}

	/**
	 * Get tiles in a specific row
	 */
	getRow(row: number): Tile[] {
		if (row < 0 || row >= GAME_CONFIG.BOARD_SIZE) {
			return [];
		}
		return [...this.tiles[row]];
	}

	/**
	 * Get tiles in a specific column
	 */
	getColumn(col: number): Tile[] {
		if (col < 0 || col >= GAME_CONFIG.BOARD_SIZE) {
			return [];
		}
		return this.tiles.map((row) => row[col]);
	}

	/**
	 * Get adjacent tiles to a position (including diagonally)
	 */
	getAdjacentTiles(row: number, col: number): Tile[] {
		const adjacent: Tile[] = [];

		for (let r = row - 1; r <= row + 1; r++) {
			for (let c = col - 1; c <= col + 1; c++) {
				if (r === row && c === col) continue; // Skip center tile
				const tile = this.getTile(r, c);
				if (tile) {
					adjacent.push(tile);
				}
			}
		}

		return adjacent;
	}

	/**
	 * Check if a path of positions is valid (adjacent tiles)
	 */
	isValidPath(positions: Position[]): boolean {
		if (positions.length < 2) return false;

		for (let i = 1; i < positions.length; i++) {
			const [prevRow, prevCol] = positions[i - 1];
			const [currRow, currCol] = positions[i];

			const prevTile = this.getTile(prevRow, prevCol);
			const currTile = this.getTile(currRow, currCol);

			if (!prevTile || !currTile) return false;
			if (!prevTile.isAdjacentTo(currTile)) return false;
		}

		return true;
	}

	/**
	 * Convert to plain object for serialization
	 */
	toObject(): BoardInterface {
		return {
			tiles: this.tiles.map((row) => row.map((tile) => tile.toObject()))
		};
	}

	/**
	 * Create Board from plain object
	 */
	static fromObject(obj: BoardInterface): Board {
		const tiles = obj.tiles.map((row) => row.map((tileObj) => Tile.fromObject(tileObj)));
		return new Board(tiles);
	}

	/**
	 * Create a random board for testing
	 */
	static createRandom(): Board {
		const tiles: Tile[][] = [];
		for (let row = 0; row < GAME_CONFIG.BOARD_SIZE; row++) {
			tiles[row] = [];
			for (let col = 0; col < GAME_CONFIG.BOARD_SIZE; col++) {
				tiles[row][col] = Tile.createRandom(row as 0 | 1 | 2 | 3 | 4, col as 0 | 1 | 2 | 3 | 4);
			}
		}
		return new Board(tiles);
	}

	/**
	 * String representation for debugging
	 */
	toString(): string {
		let result = '';
		for (let row = 0; row < GAME_CONFIG.BOARD_SIZE; row++) {
			const rowStr = this.tiles[row].map((tile) => tile.letter).join(' ');
			result += rowStr + '\n';
		}
		return result.trim();
	}

	/**
	 * Detailed string representation with multipliers
	 */
	toDetailedString(): string {
		let result = '';
		for (let row = 0; row < GAME_CONFIG.BOARD_SIZE; row++) {
			const rowStr = this.tiles[row].map((tile) => tile.toString()).join(' | ');
			result += rowStr + '\n';
		}
		return result.trim();
	}
}
