/**
 * Core type definitions for LettersBot
 *
 * These types define the fundamental game entities and their relationships
 * for the Letters game solver.
 */

// Import classes for GameNode type
import type { Board as BoardClass } from './models/Board';
import type { Word as WordClass } from './models/Word';

/**
 * Represents a single tile on the game board
 */
export interface Tile {
	/** The letter on this tile */
	letter: string;
	/** Base score value for this letter (Scrabble-style scoring) */
	base: number;
	/** Letter multiplier (1, 2, or 3) */
	letterMult: 1 | 2 | 3;
	/** Word multiplier (1, 2, or 3) */
	wordMult: 1 | 2 | 3;
	/** Column position (0-4) */
	col: 0 | 1 | 2 | 3 | 4;
	/** Row position (0-4) */
	row: 0 | 1 | 2 | 3 | 4;
}

/**
 * Position coordinate on the board
 */
export type Position = [row: number, col: number];

/**
 * Represents a word that can be played on the board
 */
export interface Word {
	/** The letters that make up this word */
	letters: string;
	/** Array of [row, col] positions for each letter */
	positions: Position[];
	/** Calculated score for this word including multipliers */
	score: number;
	/** The word value (same as letters, but kept for API compatibility) */
	value: string;
}

/**
 * Represents the 5x5 game board
 */
export interface Board {
	/** 5x5 grid of tiles */
	tiles: Tile[][];
}

/**
 * Represents the complete game state
 */
export interface GameState {
	/** Current board state */
	board: Board;
	/** Current turn number (0-5) */
	turn: 0 | 1 | 2 | 3 | 4 | 5;
	/** Total score accumulated so far */
	total: number;
	/** History of moves played */
	moves: Word[];
}

/**
 * Node in the search tree for the solver algorithm
 * Note: Uses Board and Word classes for proper method access
 */
export interface GameNode {
	/** Board state at this node */
	board: BoardClass;
	/** Turn number at this node */
	turn: 0 | 1 | 2 | 3 | 4 | 5;
	/** Total score at this node */
	total: number;
	/** Sequence of moves to reach this node */
	moves: WordClass[];
	/** Upper bound estimate for branch-and-bound pruning */
	upperBound: number;
}

/**
 * Enhanced move data with complete tile position information
 */
export interface EnhancedMove {
	/** The word letters */
	word: string;
	/** Score for this move */
	score: number;
	/** Exact tile positions selected for each letter */
	positions: Position[];
	/** Board state before this move (optional for backward compatibility) */
	boardBefore?: Tile[][];
	/** Board state after this move (optional for backward compatibility) */
	boardAfter?: Tile[][];
}

/**
 * Result data structure for storing best solutions
 */
export interface BestLineResult {
	/** Total score achieved */
	total: number;
	/** Array of words in the optimal sequence */
	words: string[];
	/** Per-round greedy best moves for comparison */
	perRound: Array<{
		word: string;
		score: number;
	}>;
	/** Enhanced move data with positions and board states (optional for backward compatibility) */
	enhancedMoves?: EnhancedMove[];
	/** Enhanced per-round data with positions (optional for backward compatibility) */
	enhancedPerRound?: EnhancedMove[];
}

/**
 * Database record structure for best_lines table
 */
export interface BestLineRecord {
	/** Date in YYYY-MM-DD format */
	date: string;
	/** JSON-serialized BestLineResult */
	data: string;
	/** Timestamp when record was created */
	created_at: string;
}

/**
 * Letter scoring values (Scrabble-style)
 */
export const LETTER_SCORES: Record<string, number> = {
	A: 1,
	B: 3,
	C: 3,
	D: 2,
	E: 1,
	F: 4,
	G: 2,
	H: 4,
	I: 1,
	J: 8,
	K: 5,
	L: 1,
	M: 3,
	N: 1,
	O: 1,
	P: 3,
	Q: 10,
	R: 1,
	S: 1,
	T: 1,
	U: 1,
	V: 4,
	W: 4,
	X: 8,
	Y: 4,
	Z: 10
} as const;

/**
 * Configuration constants
 */
export const GAME_CONFIG = {
	/** Board dimensions */
	BOARD_SIZE: 5,
	/** Number of turns in a game */
	MAX_TURNS: 5,
	/** Minimum word length */
	MIN_WORD_LENGTH: 2,
	/** Maximum word length */
	MAX_WORD_LENGTH: 25
} as const;

/**
 * Utility functions for enhanced move data
 */

/**
 * Convert a Word object to an EnhancedMove
 */
export function wordToEnhancedMove(
	word: Word,
	boardBefore?: Tile[][],
	boardAfter?: Tile[][]
): EnhancedMove {
	return {
		word: word.letters,
		score: word.score,
		positions: word.positions,
		boardBefore,
		boardAfter
	};
}

/**
 * Convert an array of Word objects to EnhancedMove array
 */
export function wordsToEnhancedMoves(words: Word[]): EnhancedMove[] {
	return words.map((word) => wordToEnhancedMove(word));
}
