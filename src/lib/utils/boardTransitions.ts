/**
 * Board state transition utilities
 *
 * This module handles the complex logic of tile dropping, replacement,
 * and board state transitions when words are played.
 */

import { Tile } from '../models/Tile';
import { Board } from '../models/Board';
import { GAME_CONFIG, LETTER_SCORES, type Word, type Position } from '../types';

/**
 * Configuration for tile generation
 */
export interface TileGenerationConfig {
	/** Letter frequency distribution for new tiles */
	letterFrequencies: Record<string, number>;
	/** Probability of generating multiplier tiles */
	multiplierProbability: number;
	/** Maximum multiplier values */
	maxLetterMult: 1 | 2 | 3;
	maxWordMult: 1 | 2 | 3;
}

/**
 * Default tile generation configuration based on English letter frequencies
 */
export const DEFAULT_TILE_CONFIG: TileGenerationConfig = {
	letterFrequencies: {
		A: 8.12,
		B: 1.49,
		C: 2.78,
		D: 4.25,
		E: 12.02,
		F: 2.23,
		G: 2.02,
		H: 6.09,
		I: 6.97,
		J: 0.15,
		K: 0.77,
		L: 4.03,
		M: 2.41,
		N: 6.75,
		O: 7.51,
		P: 1.93,
		Q: 0.1,
		R: 5.99,
		S: 6.33,
		T: 9.06,
		U: 2.76,
		V: 0.98,
		W: 2.36,
		X: 0.15,
		Y: 1.97,
		Z: 0.07
	},
	multiplierProbability: 0.15,
	maxLetterMult: 3,
	maxWordMult: 3
};

/**
 * Result of a board transition
 */
export interface TransitionResult {
	/** The new board state */
	newBoard: Board;
	/** Tiles that were removed */
	removedTiles: Tile[];
	/** New tiles that were generated */
	newTiles: Tile[];
	/** Columns that were affected */
	affectedColumns: number[];
}

/**
 * Apply a word to the board with detailed transition tracking
 */
export function applyWordWithTracking(
	board: Board,
	word: Word,
	config: TileGenerationConfig = DEFAULT_TILE_CONFIG
): TransitionResult {
	const removedTiles: Tile[] = [];
	const newTiles: Tile[] = [];
	const affectedColumns = new Set<number>();

	// Clone the board for modification
	const newBoard = board.clone();

	// Track which positions are being removed
	const removedPositions = new Set(word.positions.map(([r, c]) => `${r},${c}`));

	// Collect removed tiles
	for (const [row, col] of word.positions) {
		const tile = board.getTile(row, col);
		if (tile) {
			removedTiles.push(tile);
			affectedColumns.add(col);
		}
	}

	// Process each affected column
	for (const col of affectedColumns) {
		const columnResult = processColumnTransition(newBoard, board, col, removedPositions, config);
		newTiles.push(...columnResult.newTiles);
	}

	return {
		newBoard,
		removedTiles,
		newTiles,
		affectedColumns: Array.from(affectedColumns).sort()
	};
}

/**
 * Process tile dropping and replacement for a single column
 */
function processColumnTransition(
	board: Board,
	originalBoard: Board,
	col: number,
	removedPositions: Set<string>,
	config: TileGenerationConfig
): { newTiles: Tile[] } {
	const newTiles: Tile[] = [];

	// Collect remaining tiles in this column (from bottom to top)
	const remainingTiles: Tile[] = [];

	for (let row = GAME_CONFIG.BOARD_SIZE - 1; row >= 0; row--) {
		const posKey = `${row},${col}`;
		if (!removedPositions.has(posKey)) {
			const tile = board.getTile(row, col);
			if (tile) {
				remainingTiles.push(tile);
			}
		}
	}

	// Fill column from bottom up
	for (let row = GAME_CONFIG.BOARD_SIZE - 1; row >= 0; row--) {
		const remainingIndex = GAME_CONFIG.BOARD_SIZE - 1 - row;

		if (remainingIndex < remainingTiles.length) {
			// Use existing tile, but preserve multipliers at the target position
			const existingTile = remainingTiles[remainingIndex];
			const originalTileAtPosition = originalBoard.getTile(row, col);

			if (!originalTileAtPosition) {
				throw new Error(`No original tile found at position [${row}, ${col}]`);
			}

			// Create new tile with existing letter but original position's multipliers
			const repositionedTile = new Tile(
				existingTile.letter,
				row as 0 | 1 | 2 | 3 | 4,
				col as 0 | 1 | 2 | 3 | 4,
				originalTileAtPosition.letterMult, // Preserve original multipliers
				originalTileAtPosition.wordMult // Preserve original multipliers
			);
			board.setTile(row, col, repositionedTile);
		} else {
			// Generate new tile with random letter but preserve original position's multipliers
			const originalTileAtPosition = originalBoard.getTile(row, col);

			if (!originalTileAtPosition) {
				throw new Error(`No original tile found at position [${row}, ${col}]`);
			}

			const newTile = Tile.createRandomWithMultipliers(
				row as 0 | 1 | 2 | 3 | 4,
				col as 0 | 1 | 2 | 3 | 4,
				originalTileAtPosition.letterMult, // Preserve original multipliers
				originalTileAtPosition.wordMult // Preserve original multipliers
			);
			board.setTile(row, col, newTile);
			newTiles.push(newTile);
		}
	}

	return { newTiles };
}

/**
 * Generate a new tile with weighted letter selection
 */
export function generateNewTile(
	row: 0 | 1 | 2 | 3 | 4,
	col: 0 | 1 | 2 | 3 | 4,
	config: TileGenerationConfig = DEFAULT_TILE_CONFIG
): Tile {
	// Select letter based on frequency distribution
	const letter = selectWeightedLetter(config.letterFrequencies);

	// Determine multipliers
	const hasMultiplier = Math.random() < config.multiplierProbability;
	let letterMult: 1 | 2 | 3 = 1;
	let wordMult: 1 | 2 | 3 = 1;

	if (hasMultiplier) {
		// Randomly choose between letter and word multiplier
		if (Math.random() < 0.6) {
			// Letter multiplier (more common)
			letterMult = (Math.random() < 0.7 ? 2 : 3) as 1 | 2 | 3;
		} else {
			// Word multiplier (less common)
			wordMult = (Math.random() < 0.8 ? 2 : 3) as 1 | 2 | 3;
		}
	}

	return new Tile(letter, row, col, letterMult, wordMult);
}

/**
 * Select a letter based on weighted frequencies
 */
function selectWeightedLetter(frequencies: Record<string, number>): string {
	const letters = Object.keys(frequencies);
	const weights = Object.values(frequencies);
	const totalWeight = weights.reduce((sum, weight) => sum + weight, 0);

	let random = Math.random() * totalWeight;

	for (let i = 0; i < letters.length; i++) {
		random -= weights[i];
		if (random <= 0) {
			return letters[i];
		}
	}

	// Fallback to last letter
	return letters[letters.length - 1];
}

/**
 * Simulate multiple board transitions to predict future states
 */
export function simulateTransitions(
	board: Board,
	words: Word[],
	config: TileGenerationConfig = DEFAULT_TILE_CONFIG
): Board[] {
	const states: Board[] = [board];
	let currentBoard = board;

	for (const word of words) {
		const result = applyWordWithTracking(currentBoard, word, config);
		currentBoard = result.newBoard;
		states.push(currentBoard.clone());
	}

	return states;
}

/**
 * Analyze the stability of a board (how much it changes after transitions)
 */
export function analyzeBoardStability(
	board: Board,
	testWords: Word[],
	config: TileGenerationConfig = DEFAULT_TILE_CONFIG
): {
	stabilityScore: number;
	averageChanges: number;
	maxChanges: number;
	minChanges: number;
} {
	const changesCounts: number[] = [];

	for (const word of testWords) {
		const result = applyWordWithTracking(board, word, config);
		const changes = result.newTiles.length + result.removedTiles.length;
		changesCounts.push(changes);
	}

	if (changesCounts.length === 0) {
		return { stabilityScore: 1, averageChanges: 0, maxChanges: 0, minChanges: 0 };
	}

	const averageChanges =
		changesCounts.reduce((sum, count) => sum + count, 0) / changesCounts.length;
	const maxChanges = Math.max(...changesCounts);
	const minChanges = Math.min(...changesCounts);

	// Stability score: lower changes = higher stability
	const maxPossibleChanges = GAME_CONFIG.BOARD_SIZE * GAME_CONFIG.BOARD_SIZE;
	const stabilityScore = Math.max(0, 1 - averageChanges / maxPossibleChanges);

	return {
		stabilityScore,
		averageChanges,
		maxChanges,
		minChanges
	};
}

/**
 * Find positions that are most likely to be affected by transitions
 */
export function findVolatilePositions(board: Board): Position[] {
	const volatilePositions: Position[] = [];

	// Positions with high-value letters or multipliers are more likely to be targeted
	for (let row = 0; row < GAME_CONFIG.BOARD_SIZE; row++) {
		for (let col = 0; col < GAME_CONFIG.BOARD_SIZE; col++) {
			const tile = board.getTile(row, col);
			if (!tile) continue;

			const isHighValue = (LETTER_SCORES[tile.letter] || 0) >= 4;
			const hasMultipliers = tile.letterMult > 1 || tile.wordMult > 1;
			const isCenter = Math.abs(row - 2) <= 1 && Math.abs(col - 2) <= 1;

			if (isHighValue || hasMultipliers || isCenter) {
				volatilePositions.push([row, col]);
			}
		}
	}

	return volatilePositions;
}

/**
 * Calculate the transition entropy (randomness) of a board
 */
export function calculateTransitionEntropy(board: Board, sampleSize: number = 100): number {
	const outcomes: string[] = [];

	// Generate sample transitions with random words
	for (let i = 0; i < sampleSize; i++) {
		// Create a random word for testing
		const wordLength = 3 + Math.floor(Math.random() * 3); // 3-5 letters
		const positions: Position[] = [];

		// Generate random valid path
		let row = Math.floor(Math.random() * GAME_CONFIG.BOARD_SIZE);
		let col = Math.floor(Math.random() * GAME_CONFIG.BOARD_SIZE);
		positions.push([row, col]);

		for (let j = 1; j < wordLength; j++) {
			// Try to find adjacent position
			const directions = [
				[-1, -1],
				[-1, 0],
				[-1, 1],
				[0, -1],
				[0, 1],
				[1, -1],
				[1, 0],
				[1, 1]
			];

			const validDirections = directions.filter(([dr, dc]) => {
				const newRow = row + dr;
				const newCol = col + dc;
				return (
					newRow >= 0 &&
					newRow < GAME_CONFIG.BOARD_SIZE &&
					newCol >= 0 &&
					newCol < GAME_CONFIG.BOARD_SIZE &&
					!positions.some(([r, c]) => r === newRow && c === newCol)
				);
			});

			if (validDirections.length === 0) break;

			const [dr, dc] = validDirections[Math.floor(Math.random() * validDirections.length)];
			row += dr;
			col += dc;
			positions.push([row, col]);
		}

		if (positions.length >= 2) {
			// Extract letters and create word
			const letters = positions
				.map(([r, c]) => {
					const tile = board.getTile(r, c);
					return tile ? tile.letter : 'A';
				})
				.join('');

			try {
				const word = new (require('../models/Word').Word)(letters, positions);
				const result = applyWordWithTracking(board, word);

				// Create outcome signature
				const signature = `${result.newTiles.length}-${result.affectedColumns.length}`;
				outcomes.push(signature);
			} catch (error) {
				// Skip invalid words
			}
		}
	}

	// Calculate entropy
	const frequencies = new Map<string, number>();
	for (const outcome of outcomes) {
		frequencies.set(outcome, (frequencies.get(outcome) || 0) + 1);
	}

	let entropy = 0;
	const total = outcomes.length;

	for (const count of frequencies.values()) {
		const probability = count / total;
		entropy -= probability * Math.log2(probability);
	}

	return entropy;
}
